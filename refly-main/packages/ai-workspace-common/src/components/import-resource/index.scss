.import-resource-modal {

  .ant-modal-content {
    width: 100%;
    height: 100%;
    padding: 0;
  }

  div:has(.ant-modal-content) {
    height: 100%;
  }

  .ant-modal-body {
    height: 100%;
  }
}

.import-resource-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;

  .import-resource-left-panel {
    width: 180px;
    // background-color: #f3f3ee;
    height: 100%;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;

    .left-panel-header-title {
      font-size: 18px;
      padding-top: 24px;
      padding-left: 16px;
      padding-right: 12px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .left-panel-header-title-text {
        margin-left: 14px;
        font-weight: bold;
      }
    }

    .ant-menu-vertical {
      border-inline-end: none;
      @apply dark:bg-[#1f1f1f];
      
      .ant-menu-item-selected {
        @apply dark:bg-neutral-800 dark:text-white;
      }
      
      .ant-menu-item:hover {
        @apply dark:bg-neutral-700;
      }
    }
  }

  .import-resource-right-panel {
    flex: 1;
    height: 100%;
    // background-color: #fcfcf9;

    .intergation-content {
      height: calc(100% - 72px);
      overflow-y: auto;
      padding: 0 12px;

      @media (max-height: 600px) {
        height: calc(100% - 120px); // 为小屏幕预留更多空间
      }
    }

    .intergation-container {
      padding-top: 32px;
      position: relative;
      height: 100%;

      @media (max-height: 600px) {
        padding-top: 16px; // 减少顶部padding
      }

      .intergration-header {
        font-size: 18px;
        display: flex;
        flex-direction: row;
        align-items: center;

        .intergration-header-title {
          font-weight: bold;

          @media (max-height: 600px) {
            font-size: 16px; // 减小字体大小
          }
        }
      }
    }

    .intergation-body {
      max-width: 712px;
    }

    .intergation-body-result {
      margin-top: 24px;
    }

    .intergation-body-result-title {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.5);
      font-weight: bold;
    }

    .intergation-import-from-weblink {
      .intergation-result-list-item {
        border-radius: 0;
        border-bottom: 1px solid;
        padding: 10px 0;

        .arco-list-item {
          padding: 12px !important;
        }

        .arco-list-item-action > li:not(:last-child) {
          margin-right: 8px;
        }
      }

      .intergation-result-url {
        font-size: 12px;
        display: inline-block;
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .intergation-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    position: absolute;
    border-radius: 0;
    bottom: 0;
    padding: 16px 12px;
    border-top: 1px solid;
    width: 100%;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }

    .footer-location {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      flex: 1;

      .text-item {
        width: auto;
        white-space: nowrap;
      }

      .kg-selector {
        min-width: 200px;
        max-width: 220px;
        flex: 1;
      }

      .save-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
      }
    }

    .footer-action {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      @media (max-width: 768px) {
        justify-content: flex-start;
      }
    }
  }

  .footer-location {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;

    .footer-count {
      color: #0E9F77;
      font-weight: bold;
    }

    .text-item {
      width: auto;
      white-space: nowrap;
    }
  }

  .footer-action {
    display: flex;
    gap: 8px;

    @media (max-width: 768px) {
      justify-content: flex-end;
    }
  }
}

.keyword {
  color: #0E9F77;
}

.file-upload-container {
  .ant-upload {
    padding: 32px 24px;
  }
}

.weblink-input {
  background-color: var(--refly-bg-control-z0)!important;
  border-radius: 8px;
  padding: 6px 12px;
  &::placeholder {
    color: var(--refly-text-2);
    font-size: 12px;
    line-height: 20px;
  }
}

.file-item-container {
  .ant-spin-container {
    display: flex;
    align-items: center;
  }
}

.extension-modal {
  .ant-modal-content {
    /* Use theme tokens to support dark mode */
    background: linear-gradient(
      180deg,
      var(--refly-gradient-modal-top) 0%,
      var(--refly-gradient-modal-bottom) 25.19%
    );
    padding: 0;
    .ant-modal-body {
      padding: 0;
      width: 740px;
      height: 800px;
      overflow: hidden;
    }
  }
}
