# v0.1.1 更新日志

## **新功能支持**

- 支持侧边栏可以删除画布，以及扩展到最多展示 10 个画布，支持 hover 展示列表元素菜单栏

- 支持默认隐藏节点连线，鼠标悬浮节点仍然展示关联其他节点，支持开关节点连线，减少画布杂乱问题

- 支持画布展示更新同步状态，减少数据保存不明确带来的用户心理担忧

- 支持导入数据时，纯文本复制导入从 6000 扩张到 10W 字，可以支持较大文本内容的导入

## **核心问题优化**

- 优化浏览器语言设置自动识别，更好地适配您的语言习惯
- 新注册用户「AI 回答语言」默认设置为 Auto（自动），这样在提问的时候会根据用户提问时的语言回复对应的语言，避免回答时都是英文或者指定中文仍然回答英文的情况

- 优化追问逻辑，自动 Pin 追问的 AI 回答，基于任意资源、文档、AI 回复追问目前皆可携带上下文提问，全网搜索、知识库搜索、普通问答、生成文档等技能皆支持带上下文追问
- 预览卡片定位，现在点击节点卡片上的 「预览按钮」可以将右侧的预览卡片移动到最左边，方便定位当前操作内容

- 修复卡片上的菜单栏不出现的问题

- 出现报错或崩溃情况进入到兜底页面，提示刷新打开，不会展示错误栈页面
- 修复文档数据为空的时候，做向量/RAG 处理时报错的问题
- 画布的性能优化，避免页面不活跃时重复链接占用内存问题
- 优化画布数据同步，避免过时数据覆盖新数据的问题（包括画布标题、节点内容）
- 修复文档里面 ask ai 不遵守已经设置的输出语言的问题，比如设置了英文，仍然根据用户的输入内容的语言输出
- 优化文档里面 ask ai 输出 `<response>` 问题

- 修复切换画布之后，AI 输入框的内容没有清空的问题
- 修复「推荐问题」技能没有遵守设置的「输出语言」问题
- 修复因网络问题导致的重试提示，目前会提示模态框并进行确认
- 优化文档预览和卡片的标题同步

## **其他 Bug 修复或优化**

- 修复 N+ 边界崩溃情况
- 修复执行技能/AI 提问时报错问题
- 支持上下文选择框宽度调整，修复样式显示问题
- 修复 token 不足时，一直轮训报错的问题
- 添加登录之后加入反馈群入口
