# v0.7.0 更新日志

## 🌈 版本亮点

我们激动地宣布，Refly v0.7.0 版本正式发布，这标志着 Refly 迈入了一个激动人心的新篇章！🎉

我们自豪地推出全球首个开源的 「Vibe Workflow」 平台。现在，您只需通过自然语言下达指令，即可构建并执行复杂工作流，无缝完成从任务构想到结果交付的全过程。Vibe Workflow 融合了强大的 Agent 智能体、可自由配置的 MCP 服务器以及高度灵活的自由画布和上下文选择，为您开启 AI 驱动的自动化创作新范式，探索无限可能！

## 🌟 新功能支持

- 🤖 新增 Agent 模式（Beta）
    - **智能任务拆解**：自动将复杂任务智能拆解为一系列可执行的子任务。
    - **自动化任务执行**：依次调用 Refly 内置技能或通过 MCP 连接的外部工具，自动执行子任务，例如调用搜索引擎、访问知识库、处理文本、生成代码等
    - **动态结果评估与重新规划**：根据每个子任务返回的结果，智能评估进展。如果遇到障碍或发现更优路径，它能够动态调整后续的任务计划，直至最终目标达成
    - **端到端自动化**：实现最大程度的自动化，将您从繁琐的执行细节中解放出来
- ⚙️ MCP 服务器配置
    - **支持自定义 MCP 服务器**：现已支持配置第三方 MCP 服务器，兼容 SSE 及 Streamable 协议，极大地增强了 Refly 的功能拓展性和可玩性
    - **MCP 智能调用与手动选择**：您可以在对话中手动指定使用的 MCP，或让 AI 根据上下文自动选择并调用最合适的工具，兼具灵活性与智能化

## 🛠️ 系统优化与问题修复

🚀 性能与稳定性

- 优化本地缓存机制，彻底解决缓存溢出导致的系统崩溃
- 修复切换技能时菜单意外关闭的问题
- 修复设置默认对话模型后在部分场景不生效的问题

🌟 用户体验

- 全面升级暗黑模式，提供更舒适的视觉体验
- 优化画布节点菜单布局和功能分类，常见操作入口优化
- 解决全屏加载提示频繁触发的问题，使用体验更流畅
- 优化 AI 回答下方按钮组的交互体验
