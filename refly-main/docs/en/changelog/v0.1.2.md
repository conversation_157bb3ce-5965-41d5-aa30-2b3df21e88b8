# v0.1.2 Release Notes

## **New Feature Support**

- Supports deleting canvases from the sidebar and expanding to display up to 10 canvases, with hover display of list element menus.
- Supports default hiding of node connections, with connections still visible when hovering over nodes, and toggleable node connections to reduce canvas clutter.
- Supports displaying the update synchronization status of canvases to alleviate user concerns about data saving.
- Supports expanding the import of plain text from 6,000 to 100,000 characters, enabling the import of larger text content.

## **Core Issue Optimization**

- Optimizes automatic recognition of browser language settings to better match your language preferences.
- Sets the default "AI Answer Language" for new registered users to Auto, ensuring responses are in the same language as the user's question, avoiding responses in English or a specified language that still default to English.
- Optimizes follow-up logic by automatically pinning AI answers to follow-up questions. Follow-up questions based on any resource, document, or AI reply now carry context, and all skills including web search, knowledge base search, general Q&A, and document generation support context-carrying follow-ups.
- Improves preview card positioning; clicking the "Preview Button" on a node card now moves the right-side preview card to the far left, facilitating easier content localization.
- Fixes the issue where menu bars on cards do not appear.
- In case of errors or crashes leading to a fallback page, prompts to refresh and open, avoiding the display of error stack pages.
- Fixes errors when performing vector/RAG processing on empty document data.
- Optimizes canvas performance to prevent memory issues from repeated connections when the page is inactive.
- Enhances canvas data synchronization to prevent outdated data from overwriting new data (including canvas titles and node content).
- Fixes the issue where "Ask AI" in documents does not adhere to the set output language, e.g., still outputs in the language of the user's input even if English is set.
- Optimizes the output of `<response>` in documents when using "Ask AI."
- Fixes the issue where the content in the AI input box is not cleared after switching canvases.
- Fixes the issue where the "Recommended Questions" skill does not follow the set "Output Language."
- Improves retry prompts due to network issues, now displaying a modal box for confirmation.
- Enhances synchronization of titles in document previews and cards.

## **Other Bug Fixes or Optimizations**

- Fixes N+ boundary crash issues.
- Resolves errors when executing skills or AI questions.
- Supports adjustable width for context selection boxes and fixes style display issues.
- Fixes continuous polling errors when tokens are insufficient.
- Adds an entry to join the feedback group after logging in.
