# 功能介绍

这里将详细介绍 Refly.AI 云版本的各项功能。
Refly.AI 云版本是一个强大的可视化业务构建平台，其核心在于通过直观的画布和多样化的节点，帮助用户高效地设计、实现和管理复杂功能。主要功能包括：

*   **画布 (Canvas):** 作为核心工作区域，支持通过拖拽、连接节点来构建和编排功能流程，实现功能模块化、可视化编排和资源共享复用。
*   **节点 (Nodes):** 画布的基本构建单元，代表不同类型的资源（如文件、工具、LLM），通过连线定义交互和数据流，实现功能和数据的封装。
*   **知识库 (Knowledge Base):** 整合文档、代码、网页抓取、画布等资源，支持检索和被 AI 调用，为大模型提供丰富的上下文信息。
*   **模板 (Templates):** 允许用户将画布保存为模板，方便复用和分享，加速特定场景下的功能构建。
*   **问问 AI (Ask AI):** 集成大模型能力，通过提示词、上下文和知识库，帮助用户处理资源、生成内容，并提供小组件生成、网络搜索、知识库搜索等技能。
*   **创作工具箱 (Creation Toolbar):** 提供创建代码组件、网站节点、思维导图节点、备忘录等工具，丰富画布内容和功能。
*   **导入资源:** 支持导入全网搜索结果、上传本地文件、粘贴链接、复制文本等，便捷地将外部资源整合到画布中。

Refly.AI 云版本通过这些功能，为用户提供了一个灵活、高效且易于使用的可视化工作环境，赋能用户快速构建和迭代复杂的业务逻辑。