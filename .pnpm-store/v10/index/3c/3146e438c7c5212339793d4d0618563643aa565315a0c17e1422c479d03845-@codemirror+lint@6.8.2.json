{"name": "@codemirror/lint", "version": "6.8.2", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1756040329776, "integrity": "sha512-93AnA94LXftw4iulmPgONmsxHTOmEXXVnuDgrubQugXHDb6+kqBYV3LU04W4nfNUBsUNOpHH1CYTncof2riVDg==", "mode": 420, "size": 1118}, "dist/index.cjs": {"checkedAt": 1756040329776, "integrity": "sha512-Dl/rDWbIvlQbUdL3Va4q9+/hcTSREMoto5S4B1erqfWwdelTAB4JaGRXde98EV2ES8JUDsLZjHkhBSXQR0c+Pw==", "mode": 420, "size": 31606}, "dist/index.d.cts": {"checkedAt": 1756040329777, "integrity": "sha512-EUKWwCEV/9N8O6UdvRNgEKbXop1QFV1V4eZmRgviF/CTOUNZW3G9XcqBcT+H3Ie8ibcPxh7wusX1HOShEfCL0A==", "mode": 420, "size": 6521}, "dist/index.js": {"checkedAt": 1756040329778, "integrity": "sha512-S81UtT+v93a2Lkn6DGgxmLKlLFh6qS7KO+q+RPf2VwqoSc8D7NQhWTClqLAkwkTaFD+xqhk5n1atffQC4bFXiw==", "mode": 420, "size": 31573}, "package.json": {"checkedAt": 1756040329779, "integrity": "sha512-onFAJv5AfGrkNC4Fl01EACob+qzUgfe+9UruwRAeSFuF9Y8waVNYhFUYFhw7CnJLJ0n68/ij8J1Ev9JlEQjbyg==", "mode": 420, "size": 905}, "CHANGELOG.md": {"checkedAt": 1756040329780, "integrity": "sha512-fWrnnSZ1QgXVsc5vhtiqpHcXxIW6VfxXFjG3ygozZeMLrK4OtqEN2X3FB8g7wVXYobr/hdcrkjVOYDIawGj3OA==", "mode": 420, "size": 6202}, "README.md": {"checkedAt": 1756040329781, "integrity": "sha512-oigtqkbVpYvkJm/qlq7UOW1YR42UkS71G/ODlx1YG3rpML6zq+MxjaLgBn9vh2HDVFwwde9z3jf7VzODFr9nhg==", "mode": 420, "size": 997}, "dist/index.d.ts": {"checkedAt": 1756040329777, "integrity": "sha512-EUKWwCEV/9N8O6UdvRNgEKbXop1QFV1V4eZmRgviF/CTOUNZW3G9XcqBcT+H3Ie8ibcPxh7wusX1HOShEfCL0A==", "mode": 420, "size": 6521}, ".github/workflows/dispatch.yml": {"checkedAt": 1756040329782, "integrity": "sha512-m1oo9FDcj8HyBMn9j8PAdgJGReOxejwknSt0WhQByhphBMTHHlS1n9+UPCgcv631fi+EwCNIv0+z6VZAl52zag==", "mode": 420, "size": 415}}}