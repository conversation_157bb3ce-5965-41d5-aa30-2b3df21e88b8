# v0.2.2 Release Notes

## 🦹 Summary

🌟 Completely upgraded product onboarding experience, optimized pricing plans, and provided more intuitive knowledge base display, while fixing multiple core usage issues to make the product simpler and more stable!

## **🌟** New Features

- **📚 Comprehensive Product Onboarding Upgrade**
  - New product initialization setup wizard for easy configuration of interface language, AI response language, and operation mode (mouse/trackpad)
  - New product core feature introduction to help quickly understand product value
  - New interactive user guide with step-by-step guidance through Refly's content creation workflow
  - New hover video tutorials for feature points to intuitively understand how each function works
- **💰 Completely Optimized Pricing Plans**
  - Updated billing method: changed from Token/storage capacity to session counts and file numbers, more clear and intuitive
  - Optimized pricing description to highlight core product value
  - New usage statistics display to track usage anytime
  - Improved low usage alerts with timely replenishment guidance
- **🚀 Knowledge Base Experience Upgrade**
  - New document and resource preview function for a more intuitive and beautiful interface
- **💥 Permanently delete resources or documents**
  - Supports permanent deletion of the resource or document from the knowledge base when the node is deleted.
  - Supports deletion of all resources or documents in the canvas when the canvas is deleted.

## **💫** Core Issue Improvements

- 🌐 Resolved frequent logout issues
- 🔑 Fixed content loss when copying web content to AI documents and exporting
- 🔄 Fixed inability to delete the last character in document titles
- 📄 Fixed document editor empty image URL error
- ⚡️ Fixed abnormal navigation when deleting the last canvas in weak network conditions
