name: Deploy Docs

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'docs/**'

jobs:
  deploy:
    name: Deploy Docs
    runs-on: ubuntu-latest
    if: github.repository == 'refly-ai/refly'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
          cache-dependency-path: 'docs/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --ignore-workspace
        working-directory: docs

      - name: Build
        run: pnpm build
        working-directory: docs

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1.5.0
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: refly-docs
          branch: main
          workingDirectory: docs
          directory: .vitepress/dist
