# v0.1.1 Release Notes

## **New Feature Support**

- **Canvas Management in Sidebar**: The sidebar now supports the deletion of canvases and can expand to display up to 10 canvases. It also includes hover functionality to display list element menus.
- **Node Connection Management**: Default node connections can be hidden, with connections still visible when hovering over a node. Users can toggle node connections on or off to reduce clutter on the canvas.
- **Canvas Update Synchronization**: The canvas now displays update synchronization status, reducing user concerns about unclear data saving.
- **Enhanced Text Import**: The limit for plain text copy and paste import has been increased from 6,000 to 100,000 characters, supporting the import of larger text volumes.

## **Core Issue Optimization**

- **Browser Language Auto-Detection**: Improved automatic detection of browser language settings to better match your language preferences.
- **Default AI Response Language**: For new registrants, the default setting for "AI Response Language" is now "Auto," ensuring responses match the language of the user's query, preventing responses in English or a mismatch despite specified language settings.
- **Follow-Up Question Logic**: The AI response for follow-up questions is now automatically pinned. Contextual follow-up is supported for any resource, document, or AI reply, including web searches, knowledge base searches, general Q&A, and document generation.
- **Preview Card Positioning**: Clicking the "Preview Button" on a node card now moves the preview card to the far left, facilitating easier content localization.
- **Menu Bar Visibility**: Fixed the issue where the menu bar on cards did not appear.
- **Error Handling**: In the event of errors or crashes, users are directed to a fallback page with a refresh prompt, avoiding the display of error stack pages.
- **Document Data Handling**: Fixed the error that occurred during vector/RAG processing when document data was empty.
- **Canvas Performance Optimization**: Prevented repeated connections from consuming memory when the page is inactive.
- **Data Synchronization**: Improved canvas data synchronization to prevent outdated data from overwriting new data, including canvas titles and node content.
- **AI Language Compliance in Documents**: Fixed the issue where "Ask AI" in documents did not adhere to the set output language, ensuring consistency with user settings.
- **AI Output Formatting**: Optimized the output of `<response>` in documents.
- **AI Input Field Reset**: Fixed the issue where the AI input field was not cleared after switching canvases.
- **Skill Output Language Compliance**: Fixed the issue where the "Recommended Questions" skill did not follow the set "Output Language."
- **Retry Prompt for Network Issues**: Improved the retry prompt for network issues, now featuring a modal confirmation.
- **Document Preview and Card Title Synchronization**: Enhanced synchronization between document previews and card titles.

## **Other Bug Fixes or Optimizations**

- **Boundary Crash Fix**: Resolved the crash issue at the N+ boundary.
- **Skill/AI Query Error Handling**: Fixed errors that occurred during skill execution or AI queries.
- **Context Selection Box Width Adjustment**: Supported adjustable width for context selection boxes and fixed style display issues.
- **Token Insufficiency Handling**: Resolved the continuous error polling issue when tokens were insufficient.
- **Feedback Group Access**: Added an entry point for joining the feedback group after login.
