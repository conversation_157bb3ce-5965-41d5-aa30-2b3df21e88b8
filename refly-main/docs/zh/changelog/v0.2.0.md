# v0.2.0 更新日志

## 🦹 总结

**Refly 即将发布新的大版本 v0.2.0，支持了 8+ 重磅新能力，全面支持 AI + 画布更加直观易懂的操作体验，大幅度优化了画布性能，对标 Native/本地 App 的使用体验**，以下是一些核心能力介绍：

## **🌟** 新功能支持

- **画布新增 Memo 节点**：
  - 快速记录灵感，支持书写 Markdown，随心设置背景颜色
  - 支持从资源、文档、AI 回答快速摘录 Memo
  - 一键插入文档，辅助内容撰写
  - 主打轻量级记录，会有限制字数
- **画布新增 AI 提问节点：**
  - 画布空白处双击即可直接创建 AI 提问节点，输入问题和上下文后运行直接得到回答
  - 支持对资源、文档、AI 回答节点快速追问，添加 AI 提问节点并自动关联上下文
  - 对 AI 回答节点追问时能够自动追溯当前全部对话历史，不会再产生杂乱的连线
  - 支持多个 AI 回答并行输出
  - 支持通过 `/` 选择 AI 技能，便捷切换网络搜索、知识库搜索等技能
- **支持明星开源模型**：
  - 新增 DeepSeek V3、Qwen、Llama、Mistral 等知名开源模型
  - 所有模型均能够使用上下文、生成文档或进行网络搜索/知识库搜索
- **新增节点分组和批量操作能力：**
  - 支持对节点进行分组和取消分组
  - 支持对一组节点圈选后批量提问、添加为上下文或批量删除
  - 支持对 Group 进行整体移动整理节点
  - 支持 Group 级别的菜单操作/以及右键菜单
  - 支持 Delete 键删除节点，或批量删除节点
- **空画布引导和画布菜单优化**：
  - 新增空画布引导能力，包括快捷问 AI 和创建文档
  - 支持双击唤出菜单并在点击位置创建 AI 提问节点，进行上传资源、添加资源/文档/Memo 等操作
  - 支持右键画布操作菜单，支持配置连线显隐、AI 输入框显隐、打开或关闭点击打开预览卡片
  - 支持所有节点缩略/自适应展示等能力，支持全局维度批量自适应展示以及单个节点的自适应展示，支持节点菜单和右键菜单操作，提高画布整理能力
  - 支持搜索节点，通过快捷键上/下+回车进行节点导航
- **更好的节点菜单引导：**
  - 支持节点操作菜单在右边大卡片打开展示，提升展示可见引导效果，支持右键打开节点操作菜单，支持操作之后的产品通知反馈，支持 Hover 之后将节点优先级提高展示，解决菜单被遮盖的问题
  - 支持点击节点打开预览，支持右键菜单配置是否点击打开预览
  - 支持 Pin 预览卡片，支持默认只打开一个 Unpin 的预览卡片，添加资源/文档等默认不打开预览卡片，减少整理负担
  - 支持节点可以对子节点蔟处理布局、选中和分组操作，加强画布节点的局部整理能力
  - 支持在节点预览菜单中反向定位画布中的节点
  - 点击节点自动滚动节点预览列表到对应的节点
- **更好的 AI 回答处理**
  - 支持 AI 回答可以修改 Prompt、上下文模型等所有可配置参数并重新运行
  - 支持克隆已有的 AI 回答节点，保留问题和上下文，方便对运行结果进行比较
  - 支持在 Ask AI 节点、全局 AI 输入框中通过 `/` 唤起切换技能并增加输入框提示，增强技能使用和便捷切换引导，支持搜索技能进行切换
- **邮箱登录支持**
  - 现在可以通过邮箱直接进行注册和登录了！没有 Google 或 GitHub 账号的朋友快冲！

## **💫** 核心问题优化

- **更好的画布性能和使用体验**
  - 支持画布、文档数据本地缓存，支持类似本地 App 的使用体验，优化画布和文档的打开速度，极大优化使用体验（特别是网络环境不佳时），此外文档和画布均支持同步状态展示
  - 优化画布整体性能，包括节点拖拽、AI 回答生成拖拽卡顿问题和移动画布卡顿问题
- **更好的节点自动布局能力支持**
  - 优化生成新回答，添加新节点时位置鬼畜的问题，自动处理位置布局，减少手动编排节点成本
  - 支持节点、分组、或选中多个节点菜单，包括子节点自动布局、选中子节点、子节点建立分组等
  - 添加新节点自动定位到此节点，并优化节点位置在画布左上方，减少被遮挡问题
- **更一致的上下文列表展示**
  - 将「对话历史」统一并入上下文进行处理，减少不一致性带来的产品使用摩擦
- **更好的引用回答能力及展示**
  - 支持从 AI 回答、资源、文档中添加「引用」内容到 AI 提问，然后组合其他上下文进行 Ask AI
  - 基于文档、资源选中提问时，偶现没有选择内容的问题

## **🐞 其他 Bug 修复或优化**

- 优化快捷键删除节点之后关闭节点的预览卡片
- 修复知识库搜索有时搜不到文档的问题
- 修复画布标题更新后，无法通过全局搜索搜到的问题
- 优化编辑文档 Ask AI 时回答不遵守用户需求的问题，优化上下文的 Token 消耗
- 优化侧边栏样式，优化侧边栏和画布的标题同步问题
- 节点预览菜单支持多语翻译
- 优化技能响应的节点样式
- 优化在 AI 回答节点支持插入文档、创建文档的条件判断，不可操作时置灰，减少歧义
