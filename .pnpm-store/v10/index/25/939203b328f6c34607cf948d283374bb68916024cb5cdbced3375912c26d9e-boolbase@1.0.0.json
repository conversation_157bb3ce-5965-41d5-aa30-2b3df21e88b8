{"name": "boolbase", "version": "1.0.0", "requiresBuild": false, "files": {"package.json": {"checkedAt": 1756040329769, "integrity": "sha512-qpc0EBQ4K162BZWq+gN7xXG3GhmL4Tzpk1iqyIdb+jGFhHa2Ae+YqWpoi2rqJTCmkF4m+moJv9YIMZRViZzX+g==", "mode": 420, "size": 550}, "README.md": {"checkedAt": 1756040329770, "integrity": "sha512-/wPefbePsYnXJinr2tG66MhjLpVMKi1nC8by4XqhTah1naUeCD7ymrdcAxkoXl1pzErR1ACZ+8+dxclJ9+7fWA==", "mode": 420, "size": 655}, "index.js": {"checkedAt": 1756040329771, "integrity": "sha512-3Hd3vQVU9Rz9eG1sCGKsa2IXqEIiFwkU1EFXXorZXE/IxrB6O5jaSHDFGaxrInEg20harz60LY5zl54b667YLA==", "mode": 420, "size": 125}}}