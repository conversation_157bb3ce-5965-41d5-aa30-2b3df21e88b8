# v0.3.0 Changelog

## 🦹 Summary

🎉 Major Refly update! Comprehensive upgrades include enhanced knowledge base file processing (supporting 7+ mainstream formats), AI multimodal interaction (supporting multiple image inputs), new DeepSeek R1 chain-of-thought visualization, and improved canvas editing (with undo/redo). Experience smoother and more efficient knowledge management and content creation!

## **🌟** New Features

- **📚 Official File Upload Support for Knowledge Base**
  - Support for 7+ mainstream formats, including PDF, DOCX, RTF, TXT, MD, HTML, and EPUB
  - File extension-based icons for better visual identification
  - File download capability
- **🖼️ Multimodal AI Query Support**
  - Multiple image upload support for queries (drag & drop to input box, click to upload, or direct canvas drag & drop)
  - Compatible with various image formats: JPG, PNG, JPEG, WEBP, GIF, TIFF, BMP
  - Access to images from entire query history
  - Full-screen image preview with rotation, zoom in/out, and download options
- **🤖 AI Capability Updates**
  - DeepSeek R1 chain-of-thought process visualization
  - Other models can now query based on DeepSeek R1's chain-of-thought process
  - Support for OpenAI o3-mini model
  - Improved performance for AI web search and knowledge base search with DeepSeek R1
- **🔌 Browser Extension Enhancements**
  - Significantly improved web page content parsing accuracy, supporting most websites
  - URL and Title editing support during web clipping, optimized for text-based content capture
- **🎨 Canvas Core Features Update**
  - Undo/Redo functionality with keyboard shortcut support
  - Memo node color palette with 10+ preset colors for better canvas aesthetics
  - Improved notification display for node deletion and other operations
  - AI node title editing and complete prompt display
  - Automatic canvas naming and AI-generated canvas name suggestions
- **🚀 Resource and AI Response Node Improvements**
  - Formula rendering support
  - Mermaid diagram visualization support
  - Automatic image storage in Refly during resource import to prevent third-party Referrer issues
  - Node name editing for better identification
- **📝 Knowledge Base Operation Updates**
  - Default actions for empty content: add resource/create document

## **💫** Core Issue Fixes

- 🔧 Fixed compact mode failure caused by node stretching
- 🔍 Resolved issue where clicking 'Copy' on AI node code blocks included the 'Copy' text in clipboard
- 🖱️ Hidden node menu during drag operations
- ⚙️ Fixed context and model configuration loss during rerun 