# 视频教程

欢迎来到 Refly 视频教程集合。这些视频将通过直观的分步指导，帮助您充分利用 Refly 平台。


## 🚂 Refly：AI 原生创作平台 - 基于 LangGraph 的开源大作！

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV198fyYTENm&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 网页剪藏插件上线 - 助力构建 DeepSeek RAG 流水线！

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1R4NLeMEgH&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 0.3 发布 - 知识库继续提升！文件图片上传 + 多模态问答！

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1ExA8eEEbo&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 0.4 重大升级 - 可分享、可读网页、可定制提示词...大有可为！

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1kNRPYtEQq&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly Artifact - 提问生工件 ⚙️ 工件生万物 🦋

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1JERMYNEcJ&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 0.4.1 更新 - 生成页面成色几何？创作大挑战！

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1ZfQhYsE81&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 0.4.2 更新 - 画布模板上线 📚 分享能力再升级 ⚡️

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV15yXiYPEnN&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 应用模板 - AI 生成现代知识卡片 📇

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1JUoUY9Em1&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 开源项目｜本地部署指北 - Ollama 接入版 🦙

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV13oZzYnEWA&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 Refly 应用模板 - AI 生成自媒体封面

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1n3ZsYREGM&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🧜 Mermaid.js 语法概览｜11.6 版本全 2️⃣2️⃣ 种图表速通（上）

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1dPfFYjEx5&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🧜 Mermaid.js 语法概览｜11.6 版本全 2️⃣2️⃣ 种图表速通（下）

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV13mfTYcEJg&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 技能解锁！Refly 思维导图 ⏩ 文稿创作速成 🔮

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1ZoRBYSEnw&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🎮 趣味游戏编程挑战！Claude 🆚 Gemini 🆚 DeepSeek

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1YcR2YcENC&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>

## 🚂 技术课件模板展示 - 整合各类组件制作培训课件

<div class="video-container" style="margin-top: 12px">
  <iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=BV1QsRmYYE8J&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>
</div>


<style>
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%;
  margin-bottom: 50px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
</style> 