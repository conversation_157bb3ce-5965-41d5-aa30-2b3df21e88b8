# 🚀 Refly 0.9.0 Release Note

A milestone update focused on creative freedom, clarity, and control.

We're excited to officially release Refly 0.9.0 — a major upgrade that redefines the way you interact with AI models.

## 🌈 Highlights

- 🧮 **Credit-Based Usage**: Model usage is now powered by a flexible credit system — no need to configure API keys.
- 🎨 **Redesigned Interface**: From homepage to canvas, every touchpoint is clearer, faster, and more focused.
- 🎥 **Multimodal Access**: Use text, image, audio, and video generation seamlessly — all in one workflow.

## 🌟 New Features

### 🧮 Credit System (Cloud version)
Refly now unifies model usage under a credit-based billing system. All models consume credits transparently:

- All model types (text/image/audio/video) are billed by credit
- No more manual API key setup
- Legacy users automatically receive monthly credits
- Early unlimited members retain free access to major models (Kimi, GPT-4.1, Gemini Pro)

### 🎛 New Interface: Familiar Layout, Reimagined Experience
- Homepage now features centralized task entry, model selection, and community templates
- Canvas nodes are smoother, smarter, and more responsive
- Sidebars, preview panels, and settings now share a unified visual language
- Dark/light theme toggle supported for long-session comfort

### ⚙️ Dual Model Configuration Modes
- **Global Mode**: Automatically loads recommended models — zero configuration required
- **Custom Mode**: Allows full control over model groups and custom providers, preserving past setups

## 🛠 Performance & Bug Fixes

### Fixed Issues
- ✅ Fixed: Task timeout misjudgment for long-running model calls
- ✅ Fixed: Code generation node stuck in "processing" after completion
- ✅ Fixed: Inactive tasks not correctly marked as failed
- ✅ Fixed: Default media models failing to load in Agent mode
- ✅ Fixed: Avatar setup not initializing correctly
- ✅ Fixed: Duplicate provider install prompts & occasional install failures

## 🌟 UX Improvements

- Code blocks in AI responses now support folding/unfolding
- Improved document typography: better spacing, font clarity, and layout width for reading

## 🧭 Quick Start

- 🌐 **Website**: [https://refly.ai](https://refly.ai)
- 💰 **Plans & Pricing**: [https://refly.ai/pricing](https://refly.ai/pricing)
- ⚙️ **GitHub**: [https://github.com/refly-ai/refly](https://github.com/refly-ai/refly)
- 📩 **Business & Support**: <EMAIL>

---

Refly 0.9.0 isn't just a version update —
it's a reset on how AI tools should support creative work.

We're excited for you to try it.

As always, we're building Refly for creators like you.