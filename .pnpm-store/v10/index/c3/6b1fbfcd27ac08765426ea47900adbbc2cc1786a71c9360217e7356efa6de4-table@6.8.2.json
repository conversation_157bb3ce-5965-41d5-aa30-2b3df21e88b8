{"name": "table", "version": "6.8.2", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1756040329795, "integrity": "sha512-j8WdBZU9+zeyrrs8/IEREsYTqCH99wE10Fvc4sbjYKE9S3zyNLB6Bciei3pfBsfl4CuuIZLt7f8JtkBGeX0HAg==", "mode": 420, "size": 1529}, "dist/src/alignSpanningCell.js": {"checkedAt": 1756040329796, "integrity": "sha512-cxmDTnKo4QUKew740lIPrtCiE59ZIko6qy637Wu9C3tquVgBkj7MA8czuyakcVwBaTDEtxIe1OJJNAhp1fWkkw==", "mode": 420, "size": 2587}, "dist/src/alignString.js": {"checkedAt": 1756040329797, "integrity": "sha512-nsgXjSaLJoK2uudVRqDh0WF+TAzmONuKCIPqxtnrcsi+Yw22RYVphgNvRpnOrLStzZjCEnOJR6BVpUUeOfaA6g==", "mode": 420, "size": 2206}, "dist/src/alignTableData.js": {"checkedAt": 1756040329798, "integrity": "sha512-co3/gKpAYSWFsVfL0wu46ae43WF8tCQrtzO1M8CU1YKLhFPQRrnx+cKrZhh1ijLuJLyxt73WTQ9xppmm9aaZEg==", "mode": 420, "size": 834}, "dist/src/types/api.js": {"checkedAt": 1756040329798, "integrity": "sha512-V7OopAZ60o0e0oCoH6eSeQlXAixSZQ0mY8gnWt0I2m/Rg4y25dS6x/SnpGa5E+j8GJZ4lVpYCtFwxXtsWdybuA==", "mode": 420, "size": 108}, "dist/src/calculateCellHeight.js": {"checkedAt": 1756040329799, "integrity": "sha512-RncqXEDJ1TFewkAsNPNyjgW3B7ZTAZK4mwmwjWKEIf7dfQrKNfVePur5YQcv6quUfZWJDaIxFCOcns/qsuJlgg==", "mode": 420, "size": 497}, "dist/src/calculateMaximumColumnWidths.js": {"checkedAt": 1756040329800, "integrity": "sha512-Pq0yqYxlEnVlLpbWw+eEnY5AbxItbCe2gYJiszuELcp5dqh19ENzlI3yqipfIK5XV4OXFhq7wLGfRo0Vrl1MZg==", "mode": 420, "size": 1626}, "dist/src/calculateOutputColumnWidths.js": {"checkedAt": 1756040329801, "integrity": "sha512-DCXUUd2B2BTDpEwdZVBwalsgbqRxY0ZAVBOJU50NNMxE1Csh3CM2QJMkigJ8YVwZ+j8BM2w4woaDMe0N1qj9yw==", "mode": 420, "size": 410}, "dist/src/calculateRowHeights.js": {"checkedAt": 1756040329802, "integrity": "sha512-J/YRDkOJZempfqd/roQUoVl9LBPRmcPatbZOfERIjTnOtSUSqJMHh96X5cFviDp4CC8ngQbynwJ8vDea9SE0Tw==", "mode": 420, "size": 2293}, "dist/src/calculateSpanningCellWidth.js": {"checkedAt": 1756040329825, "integrity": "sha512-G4a4nRq1z1ODJN2gByxS2+hxwfmZLnuQpRHtjiow7TWr8Q7QW1JsejShIPV0ZiK87JZSaEAgy1lTncirTUko9g==", "mode": 420, "size": 1357}, "dist/src/createStream.js": {"checkedAt": 1756040329833, "integrity": "sha512-dws62W926jZ2Lxre63tc76RPfuwmO4EetYIxl7HFgQqveY/UyhvetNlNAiwtwKRhFhMtGinZZbzCJjQ4/7yo3Q==", "mode": 420, "size": 2989}, "dist/src/drawBorder.js": {"checkedAt": 1756040329834, "integrity": "sha512-Pho8SFUa5qPQtvQ+nCVhs3nlkSjc58vlfZYVSKLZGPjNnNrwdgtCTc6WIyYNNrnBAYvO7/j7DZgOu9xtI8O3kA==", "mode": 420, "size": 8233}, "dist/src/drawContent.js": {"checkedAt": 1756040329835, "integrity": "sha512-f6WH8VCiEWyT819d1JqW1vVr3I3kuSkwp+x6D8CJVAAxYEXFiQPhVMCMMgF1AkjQCWrD7XbNzT7hb9Hht21DvQ==", "mode": 420, "size": 2407}, "dist/src/drawRow.js": {"checkedAt": 1756040329836, "integrity": "sha512-E4mgnIomUD/8dXkQdOlck6UG+2oRXFzQertjIupTUiiMgEYS+LoNFgi3RCu7LpCaLnr5+Mz50DpEgfrOkN43zw==", "mode": 420, "size": 809}, "dist/src/drawTable.js": {"checkedAt": 1756040329837, "integrity": "sha512-gBjwmDny/0+Ocj0KaH75C3ugxjMrl7XRoJY4W8DvA8TR9xcg95V6TOYMZtvERy3baBrM9PT3TSN9mdM/Ny8YyA==", "mode": 420, "size": 1310}, "dist/src/getBorderCharacters.js": {"checkedAt": 1756040329838, "integrity": "sha512-zRJnhnsMwT9G8r1CHGRC06CqfVzPBobD0g4anbDzu2pZf2xpgrD6aZ6u7pjN6GiIFf16MC5CNR+nTjb8OqPbTA==", "mode": 420, "size": 2971}, "dist/src/index.js": {"checkedAt": 1756040329839, "integrity": "sha512-s70Nik/hkVDI8bgIncmWxzSxxk0C3Q+GhQTOJGNl3mFiY+E3nQ9BIGtw7fHWh1u8Ilp3sdkTFGm83FR/nJIt3A==", "mode": 420, "size": 1280}, "dist/src/injectHeaderConfig.js": {"checkedAt": 1756040329840, "integrity": "sha512-w4ED0Hq/L4xCzcITHS45xSmtNANgNHysQ56DE4ykpqynoih4ilFioIfu/zickwvFr58ojrhDpBQtJjWuz8VrlQ==", "mode": 420, "size": 1085}, "dist/src/types/internal.js": {"checkedAt": 1756040329841, "integrity": "sha512-N5oZX+nukqQpdMXCczWvycM6Pscxyi39gSvVWyAn3nn1CKpAc+NTcW6Qe+m2ZlrPwImbWPGdVbyGf28e45q/HQ==", "mode": 420, "size": 113}, "dist/src/makeRangeConfig.js": {"checkedAt": 1756040329841, "integrity": "sha512-6wn4mzOxbHqksYwINQ4ccVWzvJfJD54kMQXXN71X8ebO/N7gpVUiBPNlm2OIUQ2MOHfyWJTZCabqBfaHEN7Wjg==", "mode": 420, "size": 718}, "dist/src/makeStreamConfig.js": {"checkedAt": 1756040329842, "integrity": "sha512-XxhRjNOXAjkjatIEgMcZNKV/VRtP15/9hpZG8fh/7llGze5pvWHy+TbwvIwKFWIwpNIB0ZHdF8Nwdxig5hOyDg==", "mode": 420, "size": 1520}, "dist/src/makeTableConfig.js": {"checkedAt": 1756040329843, "integrity": "sha512-iFKTbcQay2tATPNBdJ1Ed5mutMcEmfd/iZohVWCSqWxAMPbZsdD5lB/xHSdBoCLv+l5Jzd/e/d8syOwIib8vAQ==", "mode": 420, "size": 2818}, "dist/src/mapDataUsingRowHeights.js": {"checkedAt": 1756040329844, "integrity": "sha512-OltXHv+oq4ksu8h6bzt6KDcOoKqT2lQ31frjBb4R251vvjSFxZwxJPDTeZR56me+J+CaZ86qjiBOlNlATXUkbQ==", "mode": 420, "size": 2406}, "dist/src/padTableData.js": {"checkedAt": 1756040329845, "integrity": "sha512-aPo8+dePVoni/KsW3CmfKgrApRquTBUDhdhItzTAE0Hbf+ni594aBSdI5yZ9+TvM19y6M1oMCdZ0FDdRj2C1DA==", "mode": 420, "size": 973}, "dist/src/spanningCellManager.js": {"checkedAt": 1756040329846, "integrity": "sha512-NyZI7vG29NDAIbyF32i8K+umf3j0J+6njOIJt71byF27yeSKkaQ8bNDSDCcjO6Yv0YRLbeRRJxSNvc5neWbhxA==", "mode": 420, "size": 4305}, "dist/src/stringifyTableData.js": {"checkedAt": 1756040329846, "integrity": "sha512-Lr9UX4OprxFkIW01D8unvGu7kHR2rSaFyzWbmNUrxq/0utgXWKSSu7CkLwixCe82ANrWs3g9BehcwUI2t3zqNQ==", "mode": 420, "size": 440}, "dist/src/table.js": {"checkedAt": 1756040329848, "integrity": "sha512-2vg4oUORtLaQNAxMSMdeASM/O1Nsc4KWQMyFHIdz0SlbqnZ88AHefHDqdLJY9KrLRSIVKVe91+Bhmq2NK7Ft9Q==", "mode": 420, "size": 1969}, "dist/src/truncateTableData.js": {"checkedAt": 1756040329849, "integrity": "sha512-l09sOKtp/6/GsSfeE2Xr8kqASXe+3WJ7RnrLsOQbV3lEzFWurT8D96Jqcrw18a/k/FYJbVM+/cmA39tujUI/wA==", "mode": 420, "size": 894}, "dist/src/utils.js": {"checkedAt": 1756040329850, "integrity": "sha512-U5k44j78hFptDXxskyioyCLCp64Rd59gcGtDLMWw4kNi4BOzi6D3LZLzc3ZW1Hs2O3UuQlnlMg+nDUy58rhAMA==", "mode": 420, "size": 4511}, "dist/src/validateConfig.js": {"checkedAt": 1756040329852, "integrity": "sha512-lcxDz4NpohImMX507dXzv5UQEzmvpJWmhg1HjvNuBwDYM0/ObAzjj86oUgnJJuuLXq70QFFWHXTaxvyTJZxmCQ==", "mode": 420, "size": 1061}, "dist/src/validateSpanningCellConfig.js": {"checkedAt": 1756040329853, "integrity": "sha512-wW8SsI+EpcKcuU856/HbMkDGlIZp+6UTonBH0wS3BHB/Uy6mb7ByeR8W02wNsM6I4SJp5g9CNuMBVckUzw0Xzg==", "mode": 420, "size": 2318}, "dist/src/validateTableData.js": {"checkedAt": 1756040329854, "integrity": "sha512-ItUesTlzqa3e+/2rS5s+R565Z338GMGRetugU2YtKHz0CwycTE4nx1LXx+CkmBISiAenvWHzDzibVavY3hM+tg==", "mode": 420, "size": 1217}, "dist/src/generated/validators.js": {"checkedAt": 1756040329856, "integrity": "sha512-UaWgWCDxUmguaDgieTX3aRynMtOqPhRdCP+OsNydyWl2JyoBCuGj4Pnnp8YGarc+hfCr7LzxnjZnu9tvjR5YvA==", "mode": 420, "size": 100210}, "dist/src/wrapCell.js": {"checkedAt": 1756040329857, "integrity": "sha512-bFaB5aX/U8tSRhInnzDuVsFGqXaSKMFX2CaXGBufntTV1rxbmgCnv/VOuSIi3OiVav+nfJnd/Y/8LH8kGZlkOw==", "mode": 420, "size": 1221}, "dist/src/wrapString.js": {"checkedAt": 1756040329858, "integrity": "sha512-yfD6fOk0GsBA9rKrzpfhQns19VOc/8vHeVLCGGuZ6puG7aiB8Xfoi0tNS9TpQJtZJkF+nX0fqlIJnBgwDmNm5A==", "mode": 420, "size": 1064}, "dist/src/wrapWord.js": {"checkedAt": 1756040329859, "integrity": "sha512-F8kDbYro+K8aKGagLpOoe8f6x8n/W5VIO6jo5/vrc9/SJgE12CI+kLRmHAXkIML9RVDiSIjAX+RjaTyMHLsawQ==", "mode": 420, "size": 1602}, "dist/src/schemas/config.json": {"checkedAt": 1756040329860, "integrity": "sha512-nKWuykCMTGlhpWh3TCrF0+IcQM31vYIcY/izz4asoTXinumqDzsr/assftNTqkWZDHftpGqy8ficRdxFHQIkbQ==", "mode": 420, "size": 2876}, "package.json": {"checkedAt": 1756040329861, "integrity": "sha512-lYPdWlHFe0niyJ4+XF7MGuKIluM9PfP+eg2aX5seHORQX81WwdOJorXm8avTjNEGvB/4zXZ8DmVNgJINr1kN+Q==", "mode": 420, "size": 2196}, "dist/src/schemas/shared.json": {"checkedAt": 1756040329863, "integrity": "sha512-hKUKufzYvWwi/sGAAQDhiDJ3pU0OtvMy0GVzBdSPgz9u4UyKaxwez4uwyJJvgb6y8UDldcB5wXsnaIcSBC8I3g==", "mode": 420, "size": 4142}, "dist/src/schemas/streamConfig.json": {"checkedAt": 1756040329864, "integrity": "sha512-lsd8qftHab/jYQY5wCU+SIbJtDRibhPtao5RDJnyt6cd7giVZfzWBLRYIdw8DzF3BAMDHyaojd4wKpTIPK0/Sg==", "mode": 420, "size": 657}, "dist/src/alignSpanningCell.js.map": {"checkedAt": 1756040329865, "integrity": "sha512-H+7D2mP0j8vLhj2Bzfw0WWuNN7M+m2rmRZKCuyHk6ByT/JJyORzcU71K1RhvUq+HECGEN/KRK+8Ayfa3bIWV1g==", "mode": 420, "size": 1886}, "dist/src/alignString.js.map": {"checkedAt": 1756040329866, "integrity": "sha512-AxTqALYi/merBco7Q9toB+0i/6nTnfcm9WlH6nGTOXTzGTPKU0JpIN2QRivWnZ4yG8gpbUUjITEY2IKbNXX6iA==", "mode": 420, "size": 1909}, "dist/src/alignTableData.js.map": {"checkedAt": 1756040329867, "integrity": "sha512-jXL9+liBN6iw8j/xpdarNZGEl8PQi3rnkhTO3BJ+QV0lRAJxf39UmNVgBSQx4A10AhXMCxqrzPV+zuXDjb79Nw==", "mode": 420, "size": 723}, "dist/src/types/api.js.map": {"checkedAt": 1756040329868, "integrity": "sha512-RmXNVlPwNZoB55t40tkHkhDUOERASZXb+iTjMx6YmmV0Bc0022Y29LJI6wbbrkblTQnf7l3FsxQoBDILWVZuwQ==", "mode": 420, "size": 110}, "dist/src/calculateCellHeight.js.map": {"checkedAt": 1756040329869, "integrity": "sha512-Ja9GINYsFsY+OuGtM85ow0xjAYyuK+cuxfsIiNQKs1W2Py1NZzRXfLYsfKtIwZX3eFqmEHQ9XNxb4Fff+rrLxA==", "mode": 420, "size": 344}, "dist/src/calculateMaximumColumnWidths.js.map": {"checkedAt": 1756040329870, "integrity": "sha512-CYgOpxgMGnqhMhfenEbHzoqr/30whG5SdIgV1ArNWP3cFOaFfrdHenM2pkkACeZNqVKwjICMX0+8kLzgjN7gTg==", "mode": 420, "size": 1223}, "dist/src/calculateOutputColumnWidths.js.map": {"checkedAt": 1756040329871, "integrity": "sha512-SgmyyFdQPL9aB8kGAGxxXDe/T2bD3VDAJgom7tACd8NmfeR/1K98JeUsXdGG0Fr/L+xV1sD82JjRsHKwtGUGWg==", "mode": 420, "size": 386}, "dist/src/calculateRowHeights.js.map": {"checkedAt": 1756040329872, "integrity": "sha512-jwZtfI9EM35tEhv+bLuxluE0j0DAB7aOD+IODNesd2osCvDAKdcf6lQNbAVoTYiL0ir/lfWuNRNYcLr49Nb9Qw==", "mode": 420, "size": 1604}, "dist/src/calculateSpanningCellWidth.js.map": {"checkedAt": 1756040329873, "integrity": "sha512-CAuJU5NzC3FP8sokEXjmzRzWv5fU20D4tCALf18p5UoqxanYaGExQwm7tYILI/herFavhP7e01PtBHMhkFMYFw==", "mode": 420, "size": 1271}, "dist/src/createStream.js.map": {"checkedAt": 1756040329874, "integrity": "sha512-K2VT0DXWxXSBGQ3wXprcX6FxoDUcKW85drfyMRMEAkXavxKKr8xSDx0f9X4pAmRGjNiuGfA7a6B0VtKxMGH5bQ==", "mode": 420, "size": 2638}, "dist/src/drawBorder.js.map": {"checkedAt": 1756040329875, "integrity": "sha512-25oMlpshzVDTQXr9Asdd3t/Fpj1Iuc1cJDj4cIhV40QgOpQ0cpW1y6dZATLxY8xEbkxZE4MWZeDmctDMkCAgRQ==", "mode": 420, "size": 6023}, "dist/src/drawContent.js.map": {"checkedAt": 1756040329875, "integrity": "sha512-udPk+bKU628CyQEXP4BuQm42adkROrjc14UBoXvhICVcLkF55myfXnvkqV5NGVmh4PkurhjApyEk8u/ce9/meQ==", "mode": 420, "size": 1858}, "dist/src/drawRow.js.map": {"checkedAt": 1756040329877, "integrity": "sha512-nkg8+XeFXBCPVil2dTUcneCBU9BY1cNYxzfJryFCIUebx69k/RpimUSgTeoJApGXvYMZ1aph8I+7CUbJzxT2Mg==", "mode": 420, "size": 651}, "dist/src/drawTable.js.map": {"checkedAt": 1756040329877, "integrity": "sha512-2WKMT0tC2pw/UaV9iLBy0BprAMNcjggTedNa2dE2bPjTP6pU399MnkfhRsMFwertPpNlgy86QCdhH1mGeHyqsQ==", "mode": 420, "size": 1110}, "dist/src/getBorderCharacters.js.map": {"checkedAt": 1756040329879, "integrity": "sha512-/FnhZgo91WwU68YrxypwZ1xlpVXMjV2zTXq3OqkwaB+t9+h6FzQE0+7z/y+L0VFjXfTi/FTVwDxzQK0nV6F6yA==", "mode": 420, "size": 2139}, "dist/src/index.js.map": {"checkedAt": 1756040329880, "integrity": "sha512-S//NgeIGGivKANVelTH3SP/IoQ+ufzTOELI8UnaFkXwPjbAVbDkB/eN/yOmHTXJ1wMEl1+BpmAA/4SJ3a/epcw==", "mode": 420, "size": 234}, "dist/src/injectHeaderConfig.js.map": {"checkedAt": 1756040329881, "integrity": "sha512-C28ZV08kPMCD1ohx/tZwzO9c9Or+KtRgDNMrcIf45nRE0UIkkkfOpm+NeSQPAOGmFwMQOvXEB+d0lJuSibflKw==", "mode": 420, "size": 1070}, "dist/src/types/internal.js.map": {"checkedAt": 1756040329882, "integrity": "sha512-GAkBGdXAaZUqHOO5wc+Jnht13xhvQTNxBrWkkL2qv24cwpva+sf8Q27yVZWDW9E+zaeBONzwldjy0DE2n7Hr7A==", "mode": 420, "size": 120}, "dist/src/makeRangeConfig.js.map": {"checkedAt": 1756040329883, "integrity": "sha512-usBimAXIEGM/I6DtTdrV769mimkh7OyneRa8NjLVcZWhlUN6y6xZ7LIz5/6M2UKHmDscXuv+jC0L+oMWWyGD5g==", "mode": 420, "size": 538}, "dist/src/makeStreamConfig.js.map": {"checkedAt": 1756040329885, "integrity": "sha512-mXvQNMLjQcJDKtBiGAStOXptmkok4YhAOrdbKRMS04JIaP4ZeDN7M8kb/v9om6qix1ln7bkjo8Z4kpismCCdOg==", "mode": 420, "size": 1064}, "dist/src/makeTableConfig.js.map": {"checkedAt": 1756040329886, "integrity": "sha512-ERMoiLqKZXS1UUfg6rnGEuUxCgN493HjIozogRYQxlmHYKVzAX8ZkfXbZckgAp7ab4zeuyUlp2i2FPGUUfAjPg==", "mode": 420, "size": 1659}, "dist/src/mapDataUsingRowHeights.js.map": {"checkedAt": 1756040329888, "integrity": "sha512-H+dGKjUlVNpLN9FhFOWQy41L4mSfrj6wltXZKyQqLlleOCp5p+PQE3RlcZTkwHVyXTxlaFnVPHub3X48abcuJg==", "mode": 420, "size": 2148}, "dist/src/padTableData.js.map": {"checkedAt": 1756040329889, "integrity": "sha512-Fs2gNxnwQiP6LH6a0Ntaw9GiTT6tBcRfu9F6uD3Peb7wCr24HI+GLnw2AcVEO+77G5Dly2wLhc0I+RBF/wi9Kg==", "mode": 420, "size": 896}, "dist/src/spanningCellManager.js.map": {"checkedAt": 1756040329891, "integrity": "sha512-dIXoPzIYou/4Xksb5St9/M8s7EVVhW37bWw7O2Hb14QWBLYfBPOXvcpk/WH5mLqe0zQF1fZ8YKnUnjNrRmfJ/w==", "mode": 420, "size": 3748}, "dist/src/stringifyTableData.js.map": {"checkedAt": 1756040329892, "integrity": "sha512-cK85Qh8ZQ/U4ybizaAbV5VgH680T7ZXFNr8Gsk2GB2vQ12MwsSdI4N0WpA9WX45OpsbWJsc7oyqNDci+NqxyCg==", "mode": 420, "size": 427}, "dist/src/table.js.map": {"checkedAt": 1756040329894, "integrity": "sha512-xOItadnpkVqX/XcZT7qBVul+y1m+TxO4x6WybjZlh2wDi7nOMD/A4NUYltGNAohA77UGc85t2NQCfSuanwI2wA==", "mode": 420, "size": 1171}, "dist/src/truncateTableData.js.map": {"checkedAt": 1756040329895, "integrity": "sha512-vdJCAN9qWLMZx3u72UkhoYi8uBu6mOrpEqguII8iexgwGTNv3VOFD71l7o2O/HT+R/gsKjLE2XJT1fVCtQ5Wxw==", "mode": 420, "size": 641}, "dist/src/utils.js.map": {"checkedAt": 1756040329897, "integrity": "sha512-p7qWirUM1iZHK5xn4+Zhlx9AMLQLEX7xfVASKAf/x0HXwpA+9UkNgf3nMZ/WWDpJ2uQIGzaH+qVGIP6Xicl/Qg==", "mode": 420, "size": 3518}, "dist/src/validateConfig.js.map": {"checkedAt": 1756040329898, "integrity": "sha512-R2E2Id6UyfqLu6GO6uCVv1KJeQ1orImVe+WXdG5UCKfqDW6K3S/TSRqgSYtfbxT/tSGi/MXv7BAvmaNW4ISi0w==", "mode": 420, "size": 739}, "dist/src/validateSpanningCellConfig.js.map": {"checkedAt": 1756040329900, "integrity": "sha512-VOE2XXnXeKis/mZjJM+8Zm4AK4cntfvMIPdbQfu78mftwK1i8PTHb9vai9y9cgTr2kK5kJmRkmuYgzv9CCJmlQ==", "mode": 420, "size": 2203}, "dist/src/validateTableData.js.map": {"checkedAt": 1756040329901, "integrity": "sha512-PB6+TSBbEi2xwlirmfHS4VTG4xZETJzcx5/0h1Hdhs6nkZrcOzfJdYGzddHNeloF9O2Xvh/HuaG2nsdHg/UD4g==", "mode": 420, "size": 992}, "dist/src/generated/validators.js.map": {"checkedAt": 1756040329902, "integrity": "sha512-CE62A1minkbnkZJcdYjPNv9m3DjiaVk32UzKT+rllqSPhgI3WxL3dOHig0G0HNiQVhSPD/mRd8IaAmJSb0Md1g==", "mode": 420, "size": 71611}, "dist/src/wrapCell.js.map": {"checkedAt": 1756040329903, "integrity": "sha512-KtpIikKiXOeKbjcYQ7PMfwQuOgETC1lL3+9Hw/d9Kwb3I8cjvWgIXcqW4o0iv2uAs5WNNsmB4M/o85KStARGFg==", "mode": 420, "size": 753}, "dist/src/wrapString.js.map": {"checkedAt": 1756040329905, "integrity": "sha512-nMovaLs7+AfP56SID+2SE0mekuLtdqsaLoH8Z3nyDFrCv7SdsLnVfhvPWjFqqNWQ57oaV63Hts9a/VFxJ5sYVQ==", "mode": 420, "size": 538}, "dist/src/wrapWord.js.map": {"checkedAt": 1756040329907, "integrity": "sha512-Z91X+bQIFWWfmBhQlbniRWRXoTLFwobR8vykYdzcucZm0xIqrBhHVoKEPm0AQYZyUBCvUMO+wB0y+mmKMriP2g==", "mode": 420, "size": 1551}, "README.md": {"checkedAt": 1756040329908, "integrity": "sha512-X7PZ9od3jc5A2vqc8m+biEPJEZwqtkCaKy3/+ohT9TowCIW4S8Zfig5cdlL1rfPAlBWi7gXC7Z/Oso5WTT7InQ==", "mode": 420, "size": 25938}, "dist/src/alignSpanningCell.d.ts": {"checkedAt": 1756040329909, "integrity": "sha512-ky6VhsTrq2VE0wveabndp2qhGskVIwhnyBNntgiZn11Bha6LxfKO3O0tHMdDLjj/vBH0Z36RcRKbNgEH/qCQuA==", "mode": 420, "size": 462}, "dist/src/alignString.d.ts": {"checkedAt": 1756040329911, "integrity": "sha512-1dB7mXb1gjsALx/IP+KdBjFXmGlmMS12zSHM58FTrU9X0bc8pOfxBcikzxAODHeIBvKyZgIZ4v5zp1MkB32Y1A==", "mode": 420, "size": 280}, "dist/src/alignTableData.d.ts": {"checkedAt": 1756040329912, "integrity": "sha512-yVX75O2QBEGUK+NySNuO0mURKmSiIlZ3wHbOIi/zHb4g7B7flTN0PHaIFdTepfToPYEpFVaRKNXTXHq9EogzmA==", "mode": 420, "size": 138}, "dist/src/types/api.d.ts": {"checkedAt": 1756040329913, "integrity": "sha512-fMf0GqnqWeHftn1comhOsZj8SpF9IY4rrhj6FqznnO/N4B4ewGdS9cZg5BFLA3CaOBCQnasWjGmQHZqyO6kTvg==", "mode": 420, "size": 4179}, "dist/src/calculateCellHeight.d.ts": {"checkedAt": 1756040329914, "integrity": "sha512-6BCKFYhg9z48zk9MZDWehApzygp7AelMdlKZB4VVCsELAAj5p0jVsPMYSUbziblP9H1+FAFmn/7jw2xkMkveuQ==", "mode": 420, "size": 200}, "dist/src/calculateMaximumColumnWidths.d.ts": {"checkedAt": 1756040329916, "integrity": "sha512-kONDDuY0ZYEQLIkZCt4CGd/r+cZx3L/nj46WfqV40MSY15pkBau2gyUBqaybyGaw0FTHvWkXBHKsAKdXupXE8w==", "mode": 420, "size": 403}, "dist/src/calculateOutputColumnWidths.d.ts": {"checkedAt": 1756040329918, "integrity": "sha512-9V/jVBXt/8vbM83BnV2NhfauDAp6YCZwqsw0LPu8JomITvOJ977RbJLDCNCjKhhbdELMyrgekGuTZVuI/8LmCA==", "mode": 420, "size": 138}, "dist/src/calculateRowHeights.d.ts": {"checkedAt": 1756040329919, "integrity": "sha512-PvY62pE7tdUCCPV5M5lwpcqUbenkrCoBOep8BmTNB7kGXPkAXbQtbdipVvLhqccKU/lK+xeVKfi3pcGNI7/Ofg==", "mode": 420, "size": 247}, "dist/src/calculateSpanningCellWidth.d.ts": {"checkedAt": 1756040329920, "integrity": "sha512-0eh25/wvS7GyTLVFSOxdWFC2Qwj30tS7K2GAWtXpQSOXwXN9WW+9cC/U+EYG7DE2LrQAIHWLLqeWAzaug78Heg==", "mode": 420, "size": 247}, "dist/src/createStream.d.ts": {"checkedAt": 1756040329922, "integrity": "sha512-iK01/oMshMWMRf2njRH8hjgFEGkoiif0LeK/WazBdeKsv7K2e4r+VFowY+uAFYEXHeJ7zzeBNFfNDnHr30kFhg==", "mode": 420, "size": 154}, "dist/src/drawBorder.d.ts": {"checkedAt": 1756040329924, "integrity": "sha512-x8n4/W7RnSHWyuUoRBF0J+9lZrhjxRfStxdtKscv9hzpFpGfMSPATo36cljG6rKymtNKB8OtfD2BXEdhWPhLmA==", "mode": 420, "size": 1747}, "dist/src/drawContent.d.ts": {"checkedAt": 1756040329925, "integrity": "sha512-QggvZfSwsDsXmmcfCDsITICtfYYwdIhLiO15yXzTKDE08rXRASjIu2IWhklkQv4TWoadIdGhsMXmg3mmuXj6Hw==", "mode": 420, "size": 542}, "dist/src/drawRow.d.ts": {"checkedAt": 1756040329927, "integrity": "sha512-E1HEMNoZ3SiQ+oaIQ3S/HoaKMSGs6enaJN+GXcY9BJLevbJslz80uP+EZ76/pLUhII/T83FD2CEcxaFfiCIEag==", "mode": 420, "size": 438}, "dist/src/drawTable.d.ts": {"checkedAt": 1756040329928, "integrity": "sha512-sEW+2wLnm7JsnZDa/8LmGVxa0QsttDloQID5TcXkNpfIy9+uwiikrxGpAyjpMB8WadXttekO3TYXY0bfF52qPA==", "mode": 420, "size": 188}, "dist/src/getBorderCharacters.d.ts": {"checkedAt": 1756040329929, "integrity": "sha512-w/mNnz6nszGIgOfXl53Bu8tjHfHZVqlSKoBmiNmO0d0SLi2517KOInHsdvMJGGoTB9If366QOola1BXuV/u7Vg==", "mode": 420, "size": 123}, "dist/src/index.d.ts": {"checkedAt": 1756040329931, "integrity": "sha512-tYErxBJ36LG4doBVXgkzIDiCVH79EE5j8ZV3KfnowFO0Vb3JLKZP7o4wmCwU9QZAoCRH53ybVpDR+MFLV+ElEw==", "mode": 420, "size": 224}, "dist/src/injectHeaderConfig.d.ts": {"checkedAt": 1756040329932, "integrity": "sha512-UvPKup6/Vjq0uzYrTclopIOCMKOi0RaINmLHbfG1NidnzxHhjM9AQPsS00kYvMnEJ2kJKVz8Xp9LQwf8gwvMjQ==", "mode": 420, "size": 231}, "dist/src/types/internal.d.ts": {"checkedAt": 1756040329934, "integrity": "sha512-TuHIj4w/TkzTTLbAAzm/nW0Db/St469J6HHMiWa4THKdi3VJKsxkE8mmZKwApXlYIjrBPEIp2oxi6+alPk94Pw==", "mode": 420, "size": 11}, "dist/src/makeRangeConfig.d.ts": {"checkedAt": 1756040329935, "integrity": "sha512-j/VPwyGb5DwF+nsOijy7rv2aCF1h3H2PB5n+T1wMmvRuxBi0Pbm58myjzLsiUaHMg/M2gmh3ARlTgYQmaE8Tgg==", "mode": 420, "size": 248}, "dist/src/makeStreamConfig.d.ts": {"checkedAt": 1756040329938, "integrity": "sha512-SmtCR/WLE7z8hM1ce8mbcu4TdBTZuArG6x4BpHxbIbo0AjOWm+btkVrtYDrM5dMITdZCc4FLjgsraWAVIKsHHg==", "mode": 420, "size": 329}, "dist/src/makeTableConfig.d.ts": {"checkedAt": 1756040329940, "integrity": "sha512-eMcbpOI9vyKymn17grB0mzugvDtGQwgIYrH1yJ3dqJll11RfBBzFUiPwWWkxHciMhsV37d/LKXm1UZ6hy0wqTw==", "mode": 420, "size": 426}, "dist/src/mapDataUsingRowHeights.d.ts": {"checkedAt": 1756040329942, "integrity": "sha512-LmaCjoscG5dbPVdn3iJaaHQXUO+2X63FvGUC0X31aeKD800blHeFCfoNlt47Z14k0CYGENXyzrwIMWtkYbzjNQ==", "mode": 420, "size": 358}, "dist/src/padTableData.d.ts": {"checkedAt": 1756040329945, "integrity": "sha512-bYZTtt9eRpfmN5vJDRPXeNXMv4CxvBOZHUFc4Yl1BHvHkxofJUZfUqTX7KlhNEKVidg2ipd062fkOJyUK1P1pA==", "mode": 420, "size": 238}, "dist/src/spanningCellManager.d.ts": {"checkedAt": 1756040329947, "integrity": "sha512-ch1Fd0VuCK2Dcolv8gt7UbfaoiTf7oZpNJ0tiqUnfkmv0diO+9Mg08XWQzTbhJldlBrTxqHS/57osceiFRu5Qw==", "mode": 420, "size": 1050}, "dist/src/stringifyTableData.d.ts": {"checkedAt": 1756040329950, "integrity": "sha512-58iQJUdNiXJhRRKNZlKVDcGNyRTY8pLrMHXHx/SvXvZ5tLO8rBodHjItTr5CO6gPPihpSqflahWu07gg8taz8w==", "mode": 420, "size": 116}, "dist/src/table.d.ts": {"checkedAt": 1756040329952, "integrity": "sha512-NF7tN/5WysYzhIWfdXht5JUHwsBwZDEgWE/al32oT2wOdrP4GWrR6ZzwxGx+YXm2Hkjm+UMaTcpgYqHmcid6NQ==", "mode": 420, "size": 141}, "dist/src/truncateTableData.d.ts": {"checkedAt": 1756040329954, "integrity": "sha512-hukxD1P+AM2LnAsWxWF/9MdEF5O8cCTAlyqtCX9AMxJ33fW2bOi9596RPUhWrcQRY7obSb8A1WHfFpcoXOqlVg==", "mode": 420, "size": 260}, "dist/src/utils.d.ts": {"checkedAt": 1756040329957, "integrity": "sha512-lEKEpwxf1rI2O2lIX/ktaJK7+ysVb1wSfFRfFdFgDrUaTpepYxFKItZlRbxMIKj9yXhN4G8Wv28VO7kT90h5Yw==", "mode": 420, "size": 722}, "dist/src/validateConfig.d.ts": {"checkedAt": 1756040329959, "integrity": "sha512-XrYU2jKp8uXK9kkNMhjUwhuo5qJkoZAGXfks0ztAQwL9P7pMeUX3rs26I56lc0UlJ9k8WN/2G/06igQoY3/QwQ==", "mode": 420, "size": 171}, "dist/src/validateSpanningCellConfig.d.ts": {"checkedAt": 1756040329961, "integrity": "sha512-gb+6Sgm/CLBV4pR1BOZhM8sYJ437tvneMnQXglMy2FlFoSQrv2hv5r8U/k0z0qQWS0oVbvL17eJKsXLqCxm1Ag==", "mode": 420, "size": 203}, "dist/src/validateTableData.d.ts": {"checkedAt": 1756040329963, "integrity": "sha512-E3q+sUt9mOC+AbMAKT8oTb18bHWL8hCJRmkJyWVqqcFt75gBP8QyayUWOfbAlEHBVqo2HQbnV2wvueOqPuJZ+Q==", "mode": 420, "size": 69}, "dist/src/generated/validators.d.ts": {"checkedAt": 1756040329965, "integrity": "sha512-g1PVHt3xghhYFiFVBzUEv3n1sLcm1Dq1TRNLvmsdOPyoj6Y3gFRtB65MwIqN0RXkwrLbY5O/UL2hXic6AgWw7A==", "mode": 420, "size": 521}, "dist/src/wrapCell.d.ts": {"checkedAt": 1756040329967, "integrity": "sha512-fjQmSzA4ZqMaJhqPKAGHK+bPidYMqd4rau3yjB26vI325qJ66P8HxR9ef1hO1AUiMzdowcFReZBbacOwd4IKHw==", "mode": 420, "size": 287}, "dist/src/wrapString.d.ts": {"checkedAt": 1756040329971, "integrity": "sha512-HEHoLwGEevS2p9braBjjerTWPHJEqI1HknkaLaRzNaCiDC7+7vWbZ9NMUE2NBNthL/em6dq2F6n3wGYGxu9tlw==", "mode": 420, "size": 369}, "dist/src/wrapWord.d.ts": {"checkedAt": 1756040329974, "integrity": "sha512-eeX/Q4VCjlhPif4FOf6TaQjkGrIQpC5M96TiYPPqO8/2evPq/ibk1cy39uXoL1k67Eho+FD9VduRBxyoxnx5bw==", "mode": 420, "size": 74}}}