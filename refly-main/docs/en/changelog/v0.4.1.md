# v0.4.1 Release Notes

## 🎯 Summary

🚀 Experience the power of next-generation AI creation with Refly v0.4.1! This game-changing update revolutionizes how you interact with AI through:

- 🧠 **Smarter AI Interactions**: Dramatically enhanced Q&A capabilities across all modes, delivering more accurate and insightful responses
- 🎨 **Visual Innovation**: Create and share beautiful visualizations with multi-component support for SVG, Mermaid, and HTML
- 💻 **Code Component Revolution**: A complete replacement for yourware.so with real-time previews and one-click deployment
- 📋 **Enhanced Canvas Experience**: New intuitive ways to create and organize your AI workspace
- 🌐 **Social Creation**: Share your AI creations, code components, and canvases with the community
- 🔄 **Seamless Collaboration**: One-click sharing of visualizations, components, and entire workspaces
- ⚡️ **Performance Boost**: Core optimizations for a smoother, more reliable experience

This release transforms Refly into your ultimate AI creative companion, making it more intuitive, powerful, and enjoyable to use than ever before. Join our growing community of creators and start sharing your AI-powered innovations today!

## 🌟 New Features

### Enhanced AI Capabilities
- **🧠 Greatly Improved Q&A Experience**
  - Optimized basic Q&A, web search, and knowledge base search results
  - Enhanced concept explanations with visual demonstrations
  - Removed memo content truncation limits for better context understanding

### Visualization & Component Features
- **🎨 Multi-Component Visualization**
  - Support for multiple SVG, Mermaid, and HTML components in a single response
  - Easy switching between preview and code views
  - Quick component creation for secondary development or fine-tuning
  - One-click download or copy to clipboard for SVG and Mermaid diagrams
  - Reference code sharing available at 👉 https://refly.ai/share/code/cod-w24oreikpvkdrt2qyl1h1w96

### Code Component Generation
- **💻 Advanced Code Component System**
  - Complete replacement for https://www.yourware.so/
  - Real-time rendering of modified code components
  - One-click deployment of web pages, SVG, and Mermaid diagrams
  - Multiple component support on canvas with sharing capabilities
  - Integration with https://ui.shadcn.com/ and Tailwind for React components
  - HTML components with https://tailwindcss.com/ support

### Canvas Improvements
- **📋 Enhanced Node Organization**
  - New AI question card creation methods
  - Component creation through card drag-and-drop
  - Right-click and double-click node menu support
  - Quick creation of AI question cards, website cards, and memos
  - Full-screen display shortcuts for AI responses and code components
  - Customizable node group names and background colors
  - Canvas example available at 👉 https://refly.ai/share/canvas/can-ufho0vka7pgxtin1g8hxgjc0

### Additional Features
- **📝 Canvas Management**
  - Support for canvas duplication
  - Efficient batch node copying and experimentation
  
- **💳 Subscription Management**
  - Support for plan upgrades (e.g., Plus to Max)
  - Contact developer required for upgrades (in-product upgrade coming soon)

## 💫 Core Improvements

- **🔧 Technical Optimizations**
  - Fixed vertical scrolling issues with preview cards
  - Resolved connection line loss problems
  - Fixed canvas existence verification after Q&A or canvas creation
  - Improved loading experience for complex canvases
  - Fixed node connection alignment issues after group dragging
  - Added memo text color customization with sharing support
  - Enhanced landing page with WeChat group access and improved display