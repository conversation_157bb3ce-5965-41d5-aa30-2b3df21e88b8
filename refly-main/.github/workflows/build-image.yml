name: <PERSON><PERSON> and Push Docker Images

on:
  workflow_dispatch:

jobs:
  build-and-push:
    name: <PERSON><PERSON> and Push Docker Images
    runs-on: ubuntu-latest
    if: github.repository == 'refly-ai/refly'
    strategy:
      matrix:
        app: ['api', 'web']
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        if: matrix.app == 'web'
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        if: matrix.app == 'web'
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        if: matrix.app == 'web'
        run: pnpm install

      - name: Build
        run: pnpm build:web
        if: matrix.app == 'web'
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
          TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
          VITE_RUNTIME: web

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: reflyai
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set Docker tags
        id: docker_tags
        run: |
          TAGS="reflyai/refly-${{ matrix.app }}:${{ github.sha }}"
          if [[ "${{ github.ref_name }}" == "main" || "${{ github.ref_name }}" == "stable" ]]; then
            TAGS="$TAGS,reflyai/refly-${{ matrix.app }}:nightly"
          fi
          echo "tags=$TAGS" >> $GITHUB_OUTPUT

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./apps/${{ matrix.app }}/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.docker_tags.outputs.tags }}
          # use cache from GitHub Actions
          # cache-from: type=gha
          # cache-to: type=gha,mode=max
