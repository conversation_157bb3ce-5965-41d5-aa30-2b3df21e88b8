# v0.6.0 更新日志

## 🚀 总结

**Refly 重磅更新：新增模型自定义配置、一键云部署、全新首页体验、幻灯片功能、批量图片处理及文档导出功能，同时优化性能与用户体验，打造更高效、稳定的 AI 原生创作平台！**

## 🌟 新功能支持

- **🔑 模型与供应商自定义配置 (BYOK)**
    - 💰 完全免费使用 Refly：接入您自己的 API Key，不消耗任何订阅额度
    - 🛠️ 全方位自定义支持： 
        - 🤖 对话模型、嵌入模型、网页搜索、资源解析均支持自定义供应商配置
        - 📋 对话模型支持自定义列表与分组（如 coding、writing 等专用模型）
        - ⚙️ 嵌入和重排模型支持精细化参数调整
- **🚢 一键云部署支持**
    - 💻 零成本部署：支持 Gitpod、Zeablur、Sealos 三大平台一键部署
    - ⚡ 即开即用：快速获取 Refly 自由画布全部能力，无需复杂配置
- **🏠 全新首页体验**
    - 🔍 智能快捷入口：支持直接在首页进行快速提问并一键创建画布
    - 📚 场景化模板库：提供丰富的常见场景快速提问入口和大量精选社区模板，帮助您快速开始工作
- **📊 幻灯片功能**
    - 🧩 灵活组织内容：支持将任意画布节点添加到幻灯片，自由排列组合
    - 🎭 专业演示模式：支持全屏幻灯片放映
    - 🔗 便捷分享：一键生成公开分享链接，轻松与团队协作
- **📸 图片处理增强**
    - 📤 批量上传：支持同时复制、拖拽、点击上传多张图片
    - 📥 多种添加方式： 
        - 📁 资源面板中直接上传并添加到画布
        - 🖱️ 拖拽多张图片到画布自动上传
        - 📋 复制多张图片后在画布一键粘贴
    - 🧑‍🎨 支持生图技能，目前还需自行配置 API Key
- **📄 文档导出功能**
    - 📑 多格式支持：支持将内容导出为 docx 和 pdf 两种常用格式
    - 🎨 保留格式：完整保留文档样式与结构

## 💫 系统优化与问题修复

- **🚀 性能与稳定性提升**
    - 🔄 解决 AI 回答时资源占用过高导致的卡顿与崩溃问题
    - 🔁 修复复制分享画布后 AI 回答异常的问题
    - 💾 优化本地缓存机制，解决缓存超限导致的页面崩溃
    - 🖼️ 修复画布为空时无法打开各类弹窗的问题
    - 🔌 解决节点连线偶尔消失的问题及相关性能优化
- **🌟 用户体验改进**
    - 📂 优化侧边栏交互，画布与知识库均支持折叠和展开
    - 📚 知识库功能优化： 
        - 💬 支持知识库提示词使用和检索分开设置
        - 🆕 空知识库时增加直观的添加入口
    - 🔄 节点操作简化： 
        - ➕ 添加 "+" 号直观指引
        - 👆 提示拖动连接、点击添加等操作
        - 🖱️ 优化右键菜单，减少操作步骤
    - 📐 改进分组内子节点的自动布局，保持相对位置，避免重叠
    - 📜 线性会话优化，打开回答时自动滚动到初始位置
    - 🔍 支持多预览窗口划线菜单，增加直接 AI 提问创建节点功能
    - 💻 优化代码组件处理，解决超大代码截断问题
    - ⌨️ 编辑器加载优化，支持超时自动降级
    - 📝 完善各类操作的友好提示

---

🙏 我们诚挚感谢所有用户的支持与反馈，Refly 团队将持续优化产品体验，为您提供更强大、更智能的创作与知识管理工具。如有任何问题或建议，欢迎随时与我们联系！