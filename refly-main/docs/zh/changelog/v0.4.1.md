# v0.4.1 更新日志

## 🎯 概述

🚀 体验 Refly v0.4.1 带来的新一代 AI 创作力！这个革命性的更新彻底改变了您与 AI 交互的方式：

- 🧠 **更智能的 AI 对话**：全方位提升问答能力，带来更准确、更有见地的回应
- 🎨 **视觉创新突破**：支持 SVG、Mermaid 和 HTML 多组件创作与分享，让创意可视化
- 💻 **代码组件革新**：完美替代 yourware.so，支持实时预览和一键部署
- 📋 **画布体验升级**：全新直观的 AI 工作空间创建与组织方式
- 🌐 **社交创作体验**：轻松分享您的 AI 创作、代码组件和画布作品
- 🔄 **无缝协作分享**：可视化内容、组件和工作空间一键分享
- ⚡️ **性能全面提升**：核心优化带来更流畅、更可靠的使用体验

这次更新将 Refly 打造成您的终极 AI 创作伙伴，让使用体验更直观、更强大、更令人愉悦。加入我们不断壮大的创作者社区，今天就开始分享您的 AI 创新成果吧！

## 🌟 新功能

### AI 能力增强
- **🧠 问答体验大幅提升**
  - 优化基础问答、全网搜索和知识库搜索效果
  - 增强概念解释，配合可视化演示
  - 移除备忘录内容截断限制，提升上下文理解

### 可视化与组件功能
- **🎨 多组件可视化**
  - 支持在单次回答中展示多个 SVG、Mermaid 和 HTML 组件
  - 便捷切换预览和代码视图
  - 快速创建组件进行二次开发或微调
  - SVG 和 Mermaid 图表一键下载或复制到剪贴板
  - 参考代码分享链接 👉 https://refly.ai/share/code/cod-w24oreikpvkdrt2qyl1h1w96

### 代码组件生成
- **💻 高级代码组件系统**
  - 完全替代 https://www.yourware.so/
  - 支持代码组件实时渲染
  - 网页、SVG、Mermaid 图表一键部署
  - 画布支持多组件，可分享整个画布或单个组件
  - React 组件集成 https://ui.shadcn.com/ 和 Tailwind
  - HTML 组件支持 https://tailwindcss.com/

### 画布优化
- **📋 节点组织增强**
  - 全新 AI 问答卡片创建方式
  - 通过卡片拖拽创建组件
  - 支持右键和双击节点菜单
  - 快速创建 AI 问答卡片、网站卡片和备忘录
  - AI 回答和代码组件快捷全屏显示
  - 自定义节点分组名称和背景颜色
  - 画布示例链接 👉 https://refly.ai/share/canvas/can-ufho0vka7pgxtin1g8hxgjc0

### 其他功能
- **📝 画布管理**
  - 支持画布复制
  - 高效批量节点复制和实验
  
- **💳 订阅管理**
  - 支持套餐升级（如 Plus 升级到 Max）
  - 目前需联系开发者进行升级（产品内升级功能即将推出）

## 💫 核心优化

- **🔧 技术优化**
  - 修复预览卡片纵向滚动问题
  - 解决连线丢失问题
  - 修复问答或画布创建后的画布验证问题
  - 优化复杂画布的加载体验
  - 修复分组拖拽后节点连线对齐问题
  - 新增备忘录文字颜色自定义及分享支持
  - 优化落地页，增加微信群入口并改进展示效果 