@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

html {
  font-size: 16px;
}

body,
#root,
html {
  @apply w-full h-full;
  margin: 0;
  padding: 0;
}
* {
  box-sizing: border-box;
}

[type='text']:focus,
input:where(:not([type])):focus,
[type='email']:focus,
[type='url']:focus,
[type='password']:focus,
[type='number']:focus,
[type='date']:focus,
[type='datetime-local']:focus,
[type='month']:focus,
[type='search']:focus,
[type='tel']:focus,
[type='time']:focus,
[type='week']:focus,
[multiple]:focus,
textarea:focus,
select:focus {
  outline: none;
  outline-offset: 0;
  --tw-ring-inset: none;
  --tw-ring-offset-width: 0;
  --tw-ring-offset-color: transparent;
  --tw-ring-color: transparent;
  --tw-ring-offset-shadow: none;
  --tw-ring-shadow: none;
  box-shadow: none;
}

.prose
  :where(blockquote p:first-of-type):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  )::before {
  content: none !important;
}

.prose
  :where(blockquote p:last-of-type):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  )::after {
  content: none !important;
}

/* Scrollbar Styles - Only show during scrolling */
@media (hover: hover) {
  * {
    scrollbar-width: auto;
    scrollbar-color: transparent transparent;
  }

  *:hover,
  *:focus,
  *:focus-within {
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 4px;
  }

  *:hover::-webkit-scrollbar-thumb,
  *:focus::-webkit-scrollbar-thumb,
  *:focus-within::-webkit-scrollbar-thumb,
  *::-webkit-scrollbar-thumb:hover {
    background-color: #c1c1c1;
    transition: background-color 0.3s ease;
  }

  *::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
  }

  .dark *:hover,
  .dark *:focus,
  .dark *:focus-within {
    scrollbar-color: #555 #2d2d2d;
  }

  .dark ::-webkit-scrollbar-track {
    background: #2d2d2d;
  }

  .dark *:hover::-webkit-scrollbar-thumb,
  .dark *:focus::-webkit-scrollbar-thumb,
  .dark *:focus-within::-webkit-scrollbar-thumb,
  .dark *::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    transition: background-color 0.3s ease;
  }

  .dark *::-webkit-scrollbar-thumb:hover {
    background-color: #777;
  }
}
html,
body {
  line-height: 1.5;
}
