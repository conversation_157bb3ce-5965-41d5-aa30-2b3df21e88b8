# 🧮 Refly 积分系统使用指南

本指南将帮助您了解 Refly 的积分系统：积分的用途、获取方式、消耗规则、套餐权益以及早期用户保留权益。

## 📌 什么是积分？

Refly 积分是一种通用计费单位，可用于调用平台上的各种 AI 模型和功能。您可以将积分理解为 AI 使用的"燃料"——每次模型调用都会根据消耗情况扣除相应的积分。

## 📥 如何获取积分？

您可以通过以下方式获得积分：

1. **订阅套餐（推荐）**
   - 每个套餐包含每日积分刷新 + 每月赠送积分
   - 套餐积分通用于所有模型，不限模态

2. **单独购买积分包（即将开放）**
   - 按需付费，补充额度更灵活

3. **参加活动/邀请好友（即将开放）**
   - 通过不定期活动获得额外奖励积分

## 💡 套餐积分规则说明

- 每日积分自动刷新，不累计至次日
- 赠送积分为一次性发放，用于高级模型或多模态调用
- 所有积分可查看历史记录与消费明细

## 👤 老用户权益说明

### ✅ 旧版会员（Plus / Pro / Max）
原有套餐保留，按月自动发放积分：
- **Plus**：700 积分/月
- **Pro**：1300 积分/月
- **Max**：2000 积分/月

可随时切换至新版套餐享更多灵活权益。

### 💎 早鸟无限会员
- 保留"无限模型使用权"
- 核心模型支持无限调用，不消耗积分，无需切换套餐

**早鸟用户可无限使用的模型如下：**
1. Claude Sonnet 4
2. Claude 4 Sonnet (thinking)
3. Kimi K2
4. GPT-4o
5. GPT-4.1
6. GPT-5
7. GPT-o3
8. GPT-OSS-120B
9. Gemini 2.5 Pro
10. Gemini 2.5 Flash
11. DeepSeek V3
12. DeepSeek R1
13. Grok 4
14. Qwen3 Coder

## 📊 如何查看积分余额与消耗情况？

您可以在以下位置查看：
- **个人中心 > 订阅**查看总积分余额与每日刷新额度
- 每次模型调用时，系统将预估并提示本次预计消耗
- 历史记录中可查看积分扣除明细与调用时间

## 💰 模型价格

### 文本模型：按 token 计费（每5K token为一单位）

| 模型 | 积分消耗 |
|------|----------|
| Claude Sonnet 4 | 约11积分 |
| Claude Opus 4.1 | 约53积分 |
| Claude 4 Sonnet (thinking) | 约11积分 |
| Kimi K2 | 约2积分 |
| GPT-4o | 约7积分 |
| GPT-4.1 | 约6积分 |
| GPT-5 | 约7积分 |
| GPT-o3 | 约6积分 |
| GPT-OSS-120B | 约1积分 |
| Gemini 2.5 Pro | 约7积分 |
| Gemini 2.5 Flash | 约2积分 |
| DeepSeek V3 0324 | 约1积分 |
| DeepSeek R1 0528 | 约2积分 |
| Grok 4 | 约10积分 |
| Qwen3 Coder | 约3积分 |

### 图像模型：按张数计费

| 模型名称 | 积分消耗 |
|----------|----------|
| Flux 1.1 pro ultra | 约9积分 |
| Flux 1.1 pro | 约6积分 |
| flux-dev | 约4积分 |
| Flux-Krea-Dev | 约4积分 |
| flux-schnell | 约1积分 |
| recraft-v3 | 约6积分 |
| recraft-v3-svg | 约12积分 |
| google / imagen-4 | 约6积分 |
| google / imagen-4-fast | 约3积分 |
| google / imagen-4-ultra | 约9积分 |
| doubao-Seedream-3.0-t2i | 约6积分 |
| Qwen-Image | 约4积分 |

### 视频模型：按秒计费

| 模型名称 | 积分消耗 |
|----------|----------|
| Veo 3 Fast (Audio) | 约448积分 |
| Veo 3 | 约840积分 |
| bytedance / seedance-1-pro | 约14积分 |
| bytedance / seedance-1-lite | 约9.8积分 |
| kwaivgi / kling-v2.1-master | 约196积分 |
| Wan 2.2 Fast | 约7积分 |

### 音频模型：按次数计费

| 模型名称 | 积分消耗 |
|----------|----------|
| suno-ai/bark | 约10积分 |
| lucataco/ace-step | 约1积分 |
| haoheliu/audio-Idm | 约10积分 |
| elevenlabs-turbo-v2.5 | 约35积分 |
| elevenlabs-multilingual-v2 | 约70积分 |

## ❓ 常见问题解答 FAQ

### Q1：什么是积分？和以前的会员有什么区别？

**A：** 积分是 Refly 新推出的通用计费单位，用于使用各类模型、生成内容、运行任务等行为。与旧版会员不同，积分更灵活，不再受套餐固定功能限制。

- **原来是**："付费订阅套餐 → 固定功能数量"
- **现在是**："付费获取积分 → 任意模型与功能按需扣除"

比如，您可以用积分：
- 调用 GPT-4、Claude 3、Gemini Pro 等多种模型
- 运行一个图像生成/文档问答/长图解读等流程
- 上传文件到知识库或构建一个智能 Agent

### Q2：旧会员怎么办？是不是强制转新套餐？

**A：** 不会。我们尊重每一位老用户的选择：
- 已开通旧套餐的用户，将继续保留原套餐、原价续费
- 系统会每月发放等值积分供您正常使用，无需额外操作
- 若想享受更高积分与更灵活的权限，可随时升级新套餐
- ⚠️ 一旦切换至新套餐，将无法再切回旧套餐

### Q3：我怎么知道用了多少积分？能控制使用量吗？

**A：** 我们提供了详细的积分消耗记录与预测提示：
- 每次生成前会显示预计消耗（如调用 Kimi K2 消耗 1 积分）
- 支持积分余额查询

### Q4：哪些模型支持？积分够用吗？

**A：** 所有主流 AI 模型都支持，并且用量透明可控：
- 支持模型包括 GPT-4.1、Gemini Pro、Kimi K2 等
- Refly 会持续优化模型调用价格，确保积分更"耐用"
- 如果积分不够，也可随时单独购买积分包补充，无需升级套餐

### Q5：我是早鸟无限会员，还需要关心积分吗？

**A：** 您拥有 Refly 的"文本模型无限绿卡"：
- 主力模型永久免费无限使用
- 不受新系统影响，也无需考虑积分消耗
- 您的权限将被特别标注，我们还会在后续上线「特别纪念墙」

### Q6：能不能免费试试？有没有新人福利？

**当然可以！** 我们为每位新注册用户准备了：

✅ **免费体验套餐**
- ✅ 每日 100 点积分刷新
- ✅ 世界顶级模型试用
- ✅ 支持上传 100 个文件

