# compiled output
/dist
/dist-electron
/node_modules
/app/models/__pycache__/**
/app/__pycache__/**

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
# Sentry Config File
.sentryclirc

# Sentry Config File
.sentryclirc

# Prisma generated files and sqlite db
src/generated
prisma/app.db
packed

.qdrant-initialized
data/lancedb

# i18n
i18n-consistency-report.json