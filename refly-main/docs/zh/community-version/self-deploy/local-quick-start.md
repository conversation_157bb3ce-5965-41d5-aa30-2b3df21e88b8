# 本地快速使用 Refly 指南

本指南旨在帮助您快速在本地部署和配置 Refly，以便开始使用其核心功能。

## 流程概览

在本地使用 Refly 的主要步骤如下：

![](/images/2025-05-14-11-08-47.png)
整个过程包括安装必要的软件、获取 Refly 代码、配置环境、启动应用，然后通过 Refly 应用界面进行供应商、模型、解析器和默认模型的配置。

## 详细步骤

### 前置要求

要自行部署 Refly，您需要安装以下软件：

-   Docker (版本 20.10.0 或更高)
-   *可选*: PostgreSQL 客户端（可以是 `psql` 或基于 GUI 的工具），用于管理可用的 LLM 模型

::: info
我们计划在未来提供功能完善的原生应用程序，以隐私为重点提供无缝的安装体验。敬请期待！
:::

### 部署步骤

#### 1. 克隆代码仓库

打开终端，运行以下命令克隆 Refly 代码仓库：

```bash
git clone https://github.com/refly-ai/refly.git
```

::: tip
如果您只需要使用 Docker 部署，可以在 `clone` 命令中添加 `--depth 1` 参数来节省磁盘空间和下载时间。
:::

#### 2. 准备环境配置

进入克隆的代码目录，并复制示例环境变量文件：

```bash
cd refly/deploy/docker
cp ../../apps/api/.env.example .env
```

::: info
所有环境变量的详细描述可以在[配置指南](./configuration.md)中查看。
:::

#### 3. 通过 docker compose 启动应用

在 `refly/deploy/docker` 目录下，运行以下命令启动 Refly 应用：

```bash
docker compose up -d
```

您可以使用 `docker ps` 命令检查容器的状态。每个容器的预期状态应该是 `Up` 和 `healthy`。

### 访问 Refly 应用

最后，您可以通过访问 `http://${HOST_IP}:5700` 来使用 Refly 应用程序，其中 `${HOST_IP}` 是主机的 IP 地址。

::: info
如果无法访问 Refly 应用，请检查以下内容：

-   `HOST_IP` 是否正确。
-   应用是否正常运行。如果未运行，请跳转到[故障排除](./index.md#troubleshooting)部分。
-   端口 `5700` 是否被任何应用程序防火墙阻止。如果您使用的是云服务器，请特别注意这一点。
:::

### 开始使用 Refly (配置 Refly)

要开始使用自部署的 Refly，首先注册一个账户，使用您的电子邮件和密码。注册并登录后，点击左下角的账户图标并选择 `Settings` 进入设置页面。

**重要提示：** 要充分使用 Refly 的核心功能，您需要按照以下步骤完成必要的配置。

#### 配置供应商

供应商为 Refly 提供实现各项高级 AI 功能所需的能力。

1.  点击设置界面的“供应商”侧边栏项。
2.  点击“添加供应商”按钮。
3.  根据您选择的供应商类型（如 OpenAI、Google、Jina、Ollama、Serper 等），填写相应的配置信息（名称、类别、API Key、Base URL 等）。详细步骤和截图请参考[个性化设置指南](../personalization.md#供应商配置)。
4.  **必须配置：** 至少添加提供 **LLM**、**嵌入 (Embedding)** 和 **重排序 (Reranker)** 能力的供应商。如果需要网页搜索功能，还需要添加提供 **webSearch** 能力的供应商（如 Serper）。

#### 配置模型

模型配置页面用于管理不同类型的模型：LLM (大型语言模型)、EMB (嵌入模型) 和排序模型。

1.  点击设置界面的“模型配置”侧边栏项。
2.  **配置 LLM 模型 (对话模型)：**
    *   在“对话模型”部分，点击“添加模型”按钮。
    *   选择对应的供应商，填写模型 ID、模型名称、上下文限制和最大输出 Tokens。详细步骤和截图请参考[个性化设置指南](../personalization.md#LLM-模型添加)。
    *   **必须配置：** 至少添加一个 **LLM (大型语言模型)** 用于对话。
3.  **配置其他模型 (嵌入模型和重排序模型)：**
    *   在“其他模型”部分，点击相应的编辑图标。
    *   **配置嵌入模型：** 选择提供嵌入能力的供应商和模型。详细步骤和截图请参考[个性化设置指南](../personalization.md#嵌入模型添加)。
        *   **必须配置：** 配置一个 **嵌入模型**（用于知识库检索）。**强调：** 嵌入模型的“维度”需要与排序模型一致。
    *   **配置排序模型：** 选择提供排序能力的供应商和模型。详细步骤和截图请参考[个性化设置指南](../personalization.md#重排模型添加)。
        *   **必须配置：** 配置一个 **重排序模型**（用于优化搜索结果）。**强调：** 排序模型的“维度”需要与嵌入模型一致。

#### 配置解析器

解析配置用于设置网页搜索、URL 解析和 PDF 解析等功能。

1.  点击设置界面的“解析配置”侧边栏项（参考截图）。
2.  根据您的需求配置相应的解析器。详细步骤和截图请参考[个性化设置指南](../personalization.md#解析配置)。
3.  **必须配置：** 如果需要使用网页搜索功能，需要在此处选择配置好的网页搜索供应商。URL 解析和 PDF 解析通常有内置选项，检查是否已配置。

#### 配置默认模型

默认模型设置可以帮助您无需每次手动选择模型。

1.  点击设置界面的“默认模型”侧边栏项（参考截图）。
2.  **必须配置：** 设置“默认问答模型”。
3.  **建议配置：** 设置“问题分析与上下文处理”和“智能标题生成”的默认模型。详细步骤和截图请参考[个性化设置指南](../personalization.md#默认解析模型)。

### 开始使用 Refly

完成以上配置步骤后，您就可以开始使用本地部署的 Refly 进行对话、构建知识库等操作了。

## 更多信息

-   [私有部署指南](.)：获取更详细的部署信息。
-   [个性化设置指南](../personalization.md)：获取更全面的配置选项说明。