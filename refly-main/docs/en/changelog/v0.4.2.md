# v0.4.2 Release Notes

## 🎯 Summary

🚀 Refly v0.4.2 has arrived with major feature upgrades and experience improvements:

- 📚 **Canvas Template System**: Complete canvas template and case library system
- 📊 **Document Table Support**: New table editing and rendering capabilities
- 🔗 **Enhanced Sharing**: Comprehensive one-click sharing solution
- 🖼️ **Preview Experience**: New image and card preview system
- 🎨 **Canvas Interaction**: More flexible node management and display
- ⚡️ **Performance Boost**: Comprehensive optimization from core to interface

This update is dedicated to providing a more professional and fluid creative experience, making your work more efficient.

## 🌟 New Features

### Canvas Templates & Case Library
- **📚 Official Canvas Template Support!**
  - Support for 30+ canvas template cases
  - Automatic template recommendations when creating new canvas
  - Ability to publish canvas as templates
  - Create canvas from template library
  - Support for duplicating existing canvases

### Document Enhancement
- **📊 Official Table Support**
  - Add and edit tables with column and row operations
  - AI-powered table generation and rendering in documents
  - One-click Markdown table import and rendering

### Sharing & Display
- **🔗 Enhanced Sharing Capabilities**
  - One-click sharing for responses, code components, and documents with shareable links

### Preview Experience
- **🖼️ Major Preview Updates**
  - Full-screen preview support for images, AI responses, resources, documents, SVG/Mermaid
  - Image zoom functionality
  - Persistent full-screen preview after card closure
  - Right-click quick preview for AI responses, code nodes, website nodes, and images

### Display Optimization
- **🎨 Enhanced Canvas Interaction**
  - Node group naming and background color customization
  - Fixed alignment issues with group dragging
  - Adjustable website node maximum width for better canvas display

### Landing Page Support
- **🌐 Landing Page Cases**
  - Canvas showcase page at https://refly.ai/use-cases-gallery
  - Artifact gallery page at https://refly.ai/artifact-gallery
  - Github README showcase support

## 💫 Core Improvements

### Performance Enhancement
- **⚡️ System Performance**
  - Fixed AI response stability issues (large webpage crawling, long output errors)
  - Significantly improved code component editor loading performance
  - Optimized code block font to monospace
  - Enhanced canvas performance with collapsible node mode for dynamic optimization

### Bug Fixes
- **🛠️ Critical Fixes**
  - Resolved infinite refresh issues with authentication
  - Fixed interaction issues with resized code component nodes
  - Resolved sharing issues with large code components (> 1500 lines)
  - Fixed various canvas duplication issues
  - Corrected free model usage counting
  - Fixed React code rendering errors in AI responses

### Experience Optimization
- **✨ Detail Refinements**
  - Cost optimization with Claude Context Cache support
  - Streamlined basic Q&A without visual Instructions
  - Fixed SVG aspect ratio and style conflicts
  - Resolved incomplete document creation from AI responses
  - Improved folding mode with global state sync and reasoning process

## 📢 Other Updates

- 🔄 Official pricing restoration 