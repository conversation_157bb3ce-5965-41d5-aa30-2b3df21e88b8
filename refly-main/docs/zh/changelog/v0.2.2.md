# v0.2.2 更新日志

## 🦹 总结

Refly 在 0.2.2 中推出多项重要更新，**包括产品定价、文档体验和核心功能优化**。

在产品定价方面，我们推出了全新的分级付费方案，包括 Plus、Pro 和 Max 三个档位，**其中 Plus 套餐年付低至 2 美元/月，并提供首年 50% 折扣优惠**。

在用户体验方面，我们全新推出了支持国际化的文档站点，优化了产品上手体验，包括新增视频介绍和优化落地页核心按钮。

同时，我们还对多个核心问题进行了修复和优化，包括改善 Safari 浏览器的白屏问题、解决技能调用相关 bug、优化文档显示和登录体验等，让产品使用更加流畅稳定。

## **🌟** 新功能支持

- **💰 新增一档 Plus 付费套餐，年付仅需 2 美元/月 ✌️**
  - 目前共包括 Plus、Pro、Max 三档套餐可选， 其中 Max 套餐可无限制使用所有模型
  - Plus 套餐包含 100 万高阶模型和 500 万基础模型使用
  - 首年订阅（需按年）限时 50% 折扣，每月仅需 2 美元
  - 付款方式支持银行卡和支付宝
- **📚 支持全新的文档站点**
  - 支持国际化文档站点，包括支持搜索、暗黑模式、多语切换
  - 全新的隐私政策和用户协议，更好的面向全球化用户
- **🎯 支持更好的上手体验**
  - 落地页新增视频介绍引导，快速了解 Refly 的核心价值
  - 优化落地页核心按钮展示体验
- **🔔 加强订阅入口感知**
  - 在输入框里面会进行一次性的提醒，引导用户订阅
  - 在侧边栏设置页面新增订阅入口

## **💫** 核心问题优化

- 🌐 优化 Safari 浏览器下的使用体验，之前会偶现白屏问题
- 🔑 修复技能调用过程中出现无权限报错问题
- 🔄 修复技能调用过程中结果被覆盖的问题
- 📄 修复刷新之后文档内容不显示的问题，修复点击节点预览文档为空的问题
- ⚡️ 优化在 Token 使用不足时，可使用模型的判断
- 🔐 优化在邮箱注册或重置密码之后没有自动登录的问题
- 🚀 修复落地页进入直接报错的问题
