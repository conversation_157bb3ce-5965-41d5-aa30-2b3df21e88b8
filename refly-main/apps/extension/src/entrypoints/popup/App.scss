body {
  padding: 8px;
  width: 380px;
  height: 456px;
  margin: 0;
  overflow: hidden;
}

#root {
  width: 100%;
  height: 100%;
}

.popup-page {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  min-height: 380px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-sizing: border-box;

  &.loading-page {
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      p {
        margin: 0;
        color: var(--color-text-2);
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .footer {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 8px;

      .shortcut-hint {
        font-size: 12px;
        color: var(--color-text-3);
        text-align: center;
        margin: 8px 0;

        .key {
          display: inline-block;
          padding: 2px 4px;
          margin: 0 4px;
          background-color: var(--color-fill-2);
          border-radius: 4px;
          font-family: monospace;
        }

        .shortcut-link {
          padding: 0 4px;
          color: rgb(var(--primary-6));

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .content-clipper-collapse {
      .ant-collapse-header {
        padding: 12px 0;
      }

      .ant-collapse-content {
        padding: 0;
      }

      .ant-collapse-content-box {
        padding: 0;
      }
    }
  }

  .content-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .state {
    font-size: 14px;
    color: var(--color-text-2);
    margin-bottom: 16px;
  }

  ul {
    margin: 8px 0;
    padding-left: 20px;
    font-size: 14px;
    color: var(--color-text-2);
  }

  .page-unsupported-hint {
    font-size: 14px;
    color: var(--color-text-2);
    margin: 16px 0;

    a {
      color: rgb(var(--primary-6));
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.logo-img {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  -webkit-user-drag: none;
}
