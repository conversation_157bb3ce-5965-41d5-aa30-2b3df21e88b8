name: Build Middleware Image

on:
  workflow_dispatch:

jobs:
  build-and-push:
    name: Build Middleware Image
    runs-on: ubuntu-latest
    if: github.repository == 'refly-ai/refly'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: reflyai
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Elasticsearch image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./deploy/docker/elasticsearch/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            reflyai/elasticsearch:7.10.2

      - name: Build and push Qdrant Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./deploy/docker/qdrant/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            reflyai/qdrant:v1.13.1
