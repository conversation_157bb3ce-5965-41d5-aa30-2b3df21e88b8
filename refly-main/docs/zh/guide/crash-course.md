# 快速上手

让我们从一个用户故事出发了解 Refly 的核心使用流程，比如我们要创建一篇关于最新发布的「OpenAI Sora」的文章。

## 注册/登录

首先访问 https://refly.ai/ ，通过邮箱或者 Google 或 GitHub 登录授权完成账号注册或登录。

## 基于自由画布梳理创作思路

进入 Refly 应用后，您可以在**自由画布**中生成一个各种和 Sora 有关技术的提问，然后了解每个部分的实现原理，当你基本上对 Sora 的技术和产品有一定了解之后，你就可以基于某个节点主题进行继续追问，深入发散，一步步了解更深的概念。

更神奇的是，每一步都代表的你的自由发散提问思考的过程，且这些过程都不会相互影响，除非您手动基于某个节点提问或者添加为上下文（**悬浮在每个节点上面的「添加上下文/追问」**），这为您提供了**基于某个主题深入追问或者基于多个主题自由思考的能力**，非常的自由！

![generate-outline](/images/generate-outline.webp)

## 使用内建 AI 搜索或上传写作素材

当你大概了解 Sora 的原理以及派生技术之后，你就可以开始准备创作的素材了！点击 Refly 左侧的悬浮按钮，选择第二个按钮「添加资源」，您可以通过**在线搜索**、复制网页链接或纯文本一键将你感兴趣的素材添加到自由画布中以用于后续的研究。

每个添加的素材会自动进行 AI 语义处理，使得您可以使用「知识库搜索」技能对所有的资源进行语义搜索提问，简直就像配备一个基于全网内容和自己私域知识的 AI 搜索引擎 Perplexity！

![explore](/images/explore.webp)

## 多线程会话进行创作研究

当您导入了待写作的素材之后，就可以基于素材进行 AI 提问，比如**对某个素材进行总结、翻译或者生成表格**，使用画布左边的第一个按钮「框选模式」或悬浮在每个节点上面的「添加上下文/追问」按钮**选择多个素材**进行总结、翻译或提出任何想问的问题！

当然，整个研究的过程中，Refly 还提供了「全网搜索」、「知识库搜索」等诸多好用的技能，可以一键调用，对全网相关的内容或者自己已经保存的所有素材进行**智能语义搜索**，不放过每一个细节！

![research](/images/research.webp)

## 使用 AI 编辑器写作

终于到了最激动人心的环节，您可以通过「生成文档」的技能快速生成一篇文档，或者使用侧边栏的「新文档」生成一篇文档，最有用的是，您在生成文档的过程中，可以选择任意画布中的素材（只需要点击悬浮在每个节点上面的「添加上下文/追问」按钮），结合素材提问然后生成文档，使得您的文档是专属于您写作目标的内容！

当然您也可以独立的完成每一个 AI 提问或者基于主题的深入探究，然后使用回答节点上的「插入」按钮，一键将有用的内容插入到文章中，这样一步一步就可以结合 AI 的辅助便捷的生成一篇可用的文档！

当然这个时候你可能会问了，我每一步都是通过 AI 生成，那么整体不是显得很乱吗？别着急！Refly 提供的文档编辑器自带丰富的富文本操作能力，您可以通过提供的富文本工具快速排版，当然，也支持一键选中内容让 AI 给你排版或者编辑，怎么样，非常贴心有木有！😆

![generate-article](/images/generate-article.webp)

好啦！通过上面的步骤，您掌握了使用 Refly AI 帮助您高效完成从想法创意到高质量内容的秘笈！接下来就教给您自由探索啦～ 祝您好运 🍀

## 常见问题解答

### 问题反馈

如果您遇到了报错，可以复制报错的排查码并立即甩在我们的脸上，我们会马上跟进修复！比如下面的报错。TraceID 即为错误码：

![error-report](/images/error-report.webp)

### 打开落地页报错

![landing-page-error](/images/landing-page-error.webp)

这种情况是因为 Refly 整体的登录授权体系发生了变化，之前参与内测已经登录的同学会遇到，只需要点击「开始使用」正常登录即可。

## 获取帮助 & 加入反馈群

访问[联系我们](/zh/community/contact-us.md)，加入我们社群，我们将第一时间为您答疑解惑！
