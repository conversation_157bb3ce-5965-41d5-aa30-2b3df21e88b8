{"name": "@ungap/structured-clone", "version": "1.2.0", "requiresBuild": false, "files": {"LICENSE": {"checkedAt": 1756040329220, "integrity": "sha512-OqTQGEp54OXbHtp0N5SZNuHZkQBYjL9pnOEbnB+kXxErxXUKhnFJ1TmFLwL0W5IbzEiFuwEGyATCeSp9slzsgA==", "mode": 420, "size": 765}, "cjs/deserialize.js": {"checkedAt": 1756040329223, "integrity": "sha512-/I861oSNQnCo/vwsg08BSbPYiV06eac/miDOuJt7ZuG/ibm+V2TWsBRRwnanLdmTeImJh6F9faOiYVXGKtKioA==", "mode": 420, "size": 1997}, "esm/deserialize.js": {"checkedAt": 1756040329224, "integrity": "sha512-1QX3nvky10D+DV3muWEfwbpQaIUUeoNorhDtUSOZlBgNo3lGaoW0gJ4HB3o13Pesg0i+CJuDVJXPmS020zpcog==", "mode": 420, "size": 1956}, "cjs/index.js": {"checkedAt": 1756040329225, "integrity": "sha512-LHh1WUrYSmtn+QlRU+en04pMrq1/Sy+mwCV27Wt2yx3rJF04OL5NjvuXVY1bpZDON5QKvn0DAlIYDne/NubOYQ==", "mode": 420, "size": 941}, "esm/index.js": {"checkedAt": 1756040329226, "integrity": "sha512-Ojeay7tlXQ/+3+Ie0ciB+XCM9E8KPCLvF31tYuZEyBEZd9q2kLKHk+GwzWkx3z5hdXwFoS/omvh3H1aN7z8qjQ==", "mode": 420, "size": 829}, "cjs/json.js": {"checkedAt": 1756040329228, "integrity": "sha512-eLgoEYCKqZ/ZICtpxOsVhVLIk9D6eUZxRz3yHTGGQKHRMy6ddSUjKTbLSSxiPq212r0miH+5he3KB6cyQwrMCA==", "mode": 420, "size": 754}, "esm/json.js": {"checkedAt": 1756040329230, "integrity": "sha512-zVO7PLueDJCAQ3GmqrUuVao8gV4nJvu8zlZJ0iEtOUibCvqG9Rwq4QLQvIb1gvmW4qecW+yZ+lDbiqcor/4zrA==", "mode": 420, "size": 690}, "cjs/serialize.js": {"checkedAt": 1756040329231, "integrity": "sha512-n+1uJl6aT4CeIXK7M1gLU6uTrgOf1bey2WAX2R1jpTFHVcMle2I6/YSPcy+ffl9krAgmvMB/aZo/lRP3ESZ3NQ==", "mode": 420, "size": 4156}, "esm/serialize.js": {"checkedAt": 1756040329232, "integrity": "sha512-Gb+5q/n9J7/Vs0D0YNtfbDzNDrWxRasuiOUxEytadVwdKbEcZ4gRr07HjmaaycGs9cvHNM4Ui5nBzRlkpfyRMw==", "mode": 420, "size": 4119}, "structured-json.js": {"checkedAt": 1756040329233, "integrity": "sha512-7tiCar4yEwMirD3+Fwl5Yai95Uv2dmpsz42Fv6SrwlL3CSBPAyAQiQctMzdnmrxNqpyHrSAf9RKdI9YiZu9RuA==", "mode": 420, "size": 2396}, "cjs/types.js": {"checkedAt": 1756040329237, "integrity": "sha512-pvXO7KiDV0ogh7kMHx7ADd5XcLVe2fI2dv1agcmncCcQ8pH5eQU794BxykjD86FV2RDS7hDxFBDi4wDYBh8erA==", "mode": 420, "size": 495}, "esm/types.js": {"checkedAt": 1756040329239, "integrity": "sha512-WyeoAFPfW8S0+Y81HWMGKH6JgmCnihs/5Z6hklANOPXJDaHXKRq8H7ZLm0XnkjZuOoYGT8J7K4sYRf3SPuJWNQ==", "mode": 420, "size": 319}, "cjs/package.json": {"checkedAt": 1756040329240, "integrity": "sha512-ZcZkWKviEBAyzdG1DKbmQ+DDaNCd+mzHAGsz7YFeEGuyD5r/EYGBgH59+fXU2Nl5Zwmx7Jp+BFRCMWNv34/fQg==", "mode": 420, "size": 19}, "package.json": {"checkedAt": 1756040329242, "integrity": "sha512-d/JuuG/JAdGzUbRBOL9CM9VY4mIxrR/y06rgCl24p76ls2crypxPWqLhrke/xMa2G+x8288A/LsPFP1srfurqg==", "mode": 420, "size": 1344}, "README.md": {"checkedAt": 1756040329244, "integrity": "sha512-dByNI6GF3TKLbh4Lw/fTmPqIsKSxyh0lzW3jej6L+8M/Pqq1CDO9H4lm3sFwS9ankZ8ThXqOFPsmrUiEyjKdSQ==", "mode": 420, "size": 4549}, ".github/workflows/node.js.yml": {"checkedAt": 1756040329249, "integrity": "sha512-fCiNbrgUAi7EvxK4AhkhkTSHlqm03kT0LrzZUYlLgcsgSNH9O6Si3B0Ao9Z8Cmf2kYA7jakhlZ7GonCCwqzMbQ==", "mode": 420, "size": 872}}}