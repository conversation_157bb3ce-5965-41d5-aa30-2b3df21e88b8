# 创作工具箱(Creation Toolbar)

## 问问 AI

参考 [问问AI](./ask-ai.md) 介绍

## 创建代码组件

- **概念：** 代码组件允许您将代码内容嵌入到画布中，并提供以下三种视图模式，以便更好地展示和编辑代码：
    1.  **预览模式：** 直观地展示代码的运行效果，方便用户快速了解代码的输出。
    2.  **代码模式：** 提供一个专注于代码编辑的界面，方便用户编写和修改代码。
    3.  **分栏视图：** 同时显示代码和预览效果，使用户可以实时查看代码修改对预览效果的影响。

    代码组件支持 AI 互动功能：

    *   用户可以针对代码内容发起 AI 提问，例如“这段代码的作用是什么？”。
    *   其他 AI 节点可以直接生成代码组件，例如根据用户需求生成一段 JavaScript 代码。

    通过代码组件，您可以将编程能力无缝地融入到创作流程中，从而提高工作效率。

- **操作：**

    1.  **新建代码组件节点：** 在工具栏中，点击 `<>` 按钮，即可在画布上创建一个新的代码组件节点。


    2.  **选择代码类型：** 在代码组件节点的下拉框中，选择您要使用的代码类型，例如 HTML、JavaScript 或 CSS。

        ![](/images/2025-04-26-22-26-35.webp)
        图：选择代码类型下拉框

    3.  **编辑代码：** 在代码编辑区域，您可以输入或粘贴您的代码。

    4.  **预览代码：** 代码组件提供预览、代码和分栏视图，方便您查看代码效果。

        *   **预览视图：** 显示代码的运行结果。

            ![](/images/2025-04-26-22-26-45.webp)
            图：SVG 组件的预览效果

        *   **代码视图：** 专注于代码编辑。

        *   **分栏视图：** 同时显示代码和预览效果，方便您实时查看代码修改对预览效果的影响。

            ![](/images/2025-04-26-22-26-57.webp)
            图：通过分栏视图修改代码并实时预览

    5.  **调整预览效果：** 在预览视图中，您可以使用右下角的按钮来放大、下载或复制图片。

        ![](/images/2025-04-26-22-27-08.webp)

    6.  **修改代码并实时预览：** 如果预览效果不理想，可以使用分栏视图，一边修改代码，一边实时查看预览效果。对于复杂的代码修改，建议使用专业的 IDE 工具。

        ![](/images/2025-04-26-22-27-25.webp)

    7.  **退出代码组件：** 修改完成后，您可以按 `Esc` 键或点击右上角的关闭按钮，返回画布界面。

        ![](/images/2025-04-26-22-27-50.webp)
## 创建网站节点

- **概念：** 网站节点功能允许您将网页内容导入到画布中，并将其转化为可交互的节点。您可以基于这些节点内容直接向 AI 提问，从而实现对网络资源的智能化利用和分析。

- **操作：**

    1.  **新建网站节点：** 在工具栏中，点击小链条按钮，即可创建一个网站节点。

        ![](/images/2025-04-26-22-28-01.webp)
        图：新建网站节点按钮

    2.  **粘贴网址并查看：** 复制您想要采集的网页 URL，粘贴到输入框内，然后点击“保存并查看网站”。网页内容将显示在右侧。

        ![](/images/2025-04-26-22-28-11.webp)
        图：粘贴网址URL并查看网页内容

    3.  **网站节点操作按钮：** 网站节点下方有三个操作按钮：
        *   红色按钮：复制当前网页的 URL。
        *   绿色按钮：在浏览器新窗口中打开该链接。
        *   蓝色按钮：编辑当前节点的 URL。

        ![](/images/2025-04-26-22-28-20.webp)
        图：网站节点的三个操作按钮

    4.  **引用网页内容提问：** 您可以将网页内容作为上下文，用于向 AI 提问。

        ![](/images/2025-04-26-22-28-35.webp)
        图：引用网页内容作为上下文提问

![](/images/2025-04-26-22-28-35.webp)
![](/images/2025-04-26-22-28-44.webp)
## 创建思维导图节点

- **概念：** 创建思维导图节点功能允许您在画布中插入交互式思维导图，以实现思维的可视化和组织。思维导图节点可以通过手动方式创建，也可以由 AI 直接生成。本质上，它是一个 Mind Map 小组件。

- **操作：**

    1.  **新建思维导图节点：** 在工具栏中，点击连接拆分样式的按钮，即可创建一个思维导图节点。

        ![](/images/2025-04-26-22-28-55.webp)
        图：新建思维导图节点按钮

    2.  **查看节点预览：** 点击思维导图节点，右侧会显示预览效果。您可以看到这是一个 Mind Map 代码小组件，其中包含了您的思维导图内容。

        ![](/images/2025-04-26-22-29-05.webp)
        图：点击节点，右侧出现预览效果

    3.  **调整思维导图尺寸：** 通过点击右下角的大小控制按钮，您可以调整思维导图的尺寸。

        ![](/images/2025-04-26-22-29-14.webp)
        图：通过点击右下角大小控制按钮，调整思维导图的尺寸

    4.  **最大化预览视图：** 为了获得更好的使用体验，建议在预览视图下先点击预览按钮，然后点击最大化按钮，进入思维导图的理想使用界面。

        ![](/images/2025-04-26-22-29-25.webp)
        图：思维导图的理想使用界面

    5.  **编辑分支节点：** 将鼠标悬停在分支节点上，会出现悬浮按钮，您可以进行以下操作：
        *   **编辑分支：** 以卡片形式编辑分支内容。
        *   **添加子卡片：** 创建下一级分支。
        *   **添加同级卡片：** 添加与当前卡片同级的新卡片。

        ![](/images/2025-04-26-22-29-41.webp)
        图：编辑分支，添加子卡片，创建下一级分支

    6.  **复制卡片内容：** 点击复制按钮，可以将卡片内的内容复制到剪贴板，方便粘贴到其他位置。

        ![](/images/2025-04-26-22-29-50.webp)
        图：点击复制，复制卡片里面的内容到剪贴板

    7.  **修改卡片内容和格式：** 点击卡片内部，可以修改卡片的内容。同时会出现悬浮工具栏，您可以修改卡片的底色，以及调整内容的字号、颜色等格式。

        ![](/images/2025-04-26-22-30-19.webp)
        图：点击卡片内部，可以修改卡片里的内容

    8.  **删除卡片：** 选中要删除的卡片，点击删除卡片按钮。这将删除当前卡片及其所有下一级分支。

        ![](/images/2025-04-26-22-30-30.webp)
        图：选中要删除的卡片，点击删除卡片

        ![](/images/2025-04-26-22-30-39.webp)
        图：删除卡片

        ![](/images/2025-04-26-22-30-49.webp)
        图：删除卡片
## 创建备忘录

- **概念：** 备忘录功能允许您在画布上添加便签式注释，用于记录重要信息或作为内容分隔标识。这特别适用于组织和说明复杂画布中的内容。

- **操作：**

    1.  **创建备忘录节点：** 点击创建备忘录按钮，画布上会生成一个默认的浅黄色备忘录节点。

        ![](/images/2025-04-26-22-31-02.webp)
        图：点击创建备忘录按钮，默认生成一个浅黄色节点

    2.  **编辑备忘录内容：** 点击备忘录节点内部，即可编辑内容。同时，上方会出现编辑工具悬浮窗，您可以修改备忘录的颜色，以及调整内容的字号、颜色等格式。备忘录右下角会显示创建至今的时间。

        ![](/images/2025-04-26-22-31-21.webp)
        图：编辑备忘录节点内部，可以编辑内容

    3.  **调整备忘录大小：** 通过拖动备忘录四角的圆点，您可以调整备忘录节点的大小。

        ![](/images/2025-04-26-22-31-37.webp)
        图：通过拖动四角圆点，调整备忘录节点大小

    4.  **备忘录使用场景：** 以下图片展示了备忘录的使用场景。

        ![](/images/2025-04-26-22-31-44.webp)
        图：备忘录使用场景展示

## 导入资源

### 全网搜索

- **概念：** 全网搜索功能允许您将互联网搜索结果直接导入到画布中，方便进行内容整合与分析。

![](/images/2025-04-26-22-31-57.webp)
图：全网搜索

### 上传文件

- **概念：** 文件上传功能允许您将本地文件导入到画布中，以便进行内容的便捷管理与处理。
- **操作：** 您可以通过点击或拖拽文件来上传。

支持以下文件类型：

*   PDF
*   DOCX
*   RTF
*   TXT
*   MD
*   HTML
*   EPUB

每个文件最大支持 30MB。文件解析会消耗使用额度，不同套餐的额度数量不同，额度按天计算。

![](/images/2025-04-26-22-32-16.webp)
图：上传文件

### 粘贴链接

- **操作：** 您可以将选好的链接粘贴到输入框中，将其添加到画布里。

![](/images/2025-04-26-22-32-24.webp)
图：粘贴链接

添加成功后，链接会进入待处理列表。

![](/images/2025-04-26-22-32-38.webp)
图：添加到待处理列表

### 复制文本

- **概念：** 复制文本功能允许您输入或粘贴文本内容。标题和链接是可选的，但文本内容是必填项。此功能可以提高效率，减少错误，节省时间，方便从其他来源快速迁移内容。
- **操作：** 从其他来源复制内容后，直接粘贴即可。标题和链接可以留空。文本内容栏为必填项，有红色星号提示。

![](/images/2025-04-26-22-32-47.webp)
图：复制文本

添加到画布后，会生成一个资源节点。

![](/images/2025-04-26-22-32-59.webp)
图：资源节点

### 插件剪存

- **概念：** 浏览器插件功能允许您通过浏览器插件一键将网页内容直接添加到画布中，实现高效的内容收集与整理。
- **操作：** 您可以观看页面上的视频介绍，或点击以下链接查看详细文档：

[浏览器插件文档](https://docs.refly.ai/zh/guide/chrome-extension)

![](/images/2025-04-26-22-33-09.webp)
图：插件剪存

## 添加资源

- **概念：** 在 Refly 中，无论是从互联网获取的内容、上传的文件，还是 Refly 生成的内容，都以资源节点的形式存在。通过“添加资源”功能，您可以将这些资源添加到当前画布中进行二次使用。

- **操作：**

    1.  在“创作工具箱”处，点击“添加资源”按钮。
    2.  选择需要添加到画布的资源。
    3.  点击“确定”后，所选资源将添加到当前打开的画布中。

图1：添加资源
![](/images/2025-04-26-22-33-23.webp)

图2：添加资源
![](/images/2025-04-26-22-33-32.webp)

## 新建文档

- **概念：** “新建文档”功能允许您创建一个空白文档，适用于您需要自己编写内容或从其他地方复制内容到文档中。创建后的文档将作为一个资源节点，可以在 Refly 的其他组件中使用。

- **操作：**

    1.  在“创作工具箱”处，点击“新建文档”按钮。
    2.  稍等片刻，新文档将添加到当前画布中。

图1：新建文档
![](/images/2025-04-26-22-33-43.webp)

图2：新建文档
![](/images/2025-04-26-22-33-52.webp)

## 添加文档

- **概念：** “添加文档”功能允许您导入在任意画布中已创建的文档，从而实现文档的跨画布复用。（关于新建文档的方法，请参阅前述“新建文档”功能说明。）

![](/images/2025-04-26-22-34-41.webp)
图：添加文档

- **操作：**

    1.  在“创作工具箱”处，点击“添加文档”按钮。
    2.  搜索文档标题，或滚动查找您需要添加的文档。
    3.  选中文档后，点击右下角“确认”按钮。
    4.  文档添加成功。

![](/images/2025-04-26-22-34-47.webp)
图：添加文档