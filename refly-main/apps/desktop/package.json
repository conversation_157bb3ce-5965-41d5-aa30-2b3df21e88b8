{"name": "refly-desktop", "version": "0.8.0", "description": "Refly Desktop Application", "main": "dist/main.js", "scripts": {"dev": "nodemon", "dev:electron": "nodemon", "start": "tsc && tsc-alias --project tsconfig.json && electron .", "copy-renderer": "ncp ../web/dist dist/renderer", "build:mac": "tsc && electron-builder --mac --dir", "build:mac:x64": "tsc && electron-builder --mac --x64 --dir", "build:mac:arm64": "tsc && electron-builder --mac --arm64 --dir", "build:win": "tsc && electron-builder --win --dir", "build:win:x64": "tsc && electron-builder --win --x64 --dir", "build:win:arm64": "tsc && electron-builder --win --arm64 --dir", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@electron/asar": "^4.0.0", "@types/express": "^4.17.13", "@types/multer": "~1.4.11", "electron": "36.2.1", "electron-builder": "26.0.15", "electron-notarize": "^1.2.2", "tsconfig-paths": "~4.2.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16"}, "dependencies": {"@prisma/engines": "5.16.1", "@refly/api": "workspace:*", "electron-log": "^5.4.0", "get-port": "^7.1.0", "is-number": "^7.0.0", "prisma": "5.16.1"}}