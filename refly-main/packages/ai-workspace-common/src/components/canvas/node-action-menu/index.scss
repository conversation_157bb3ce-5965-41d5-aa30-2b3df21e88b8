.node-action-menu-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to bottom, transparent, white 40%);
  height: 24px;
  border-radius: 0 0 8px 8px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: linear-gradient(to bottom, transparent, #f5f5f5 40%);
    @apply dark:bg-gradient-to-b dark:from-transparent dark:to-gray-700;
  }

  // 添加暗色模式支持
  @apply dark:bg-gradient-to-b dark:from-transparent dark:to-gray-900;
}
